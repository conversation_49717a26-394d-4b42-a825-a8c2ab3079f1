"use strict";var jt=Object.create;var ze=Object.defineProperty;var Dt=Object.getOwnPropertyDescriptor;var Nt=Object.getOwnPropertyNames;var qt=Object.getPrototypeOf,Qt=Object.prototype.hasOwnProperty;var zt=(t,n)=>{for(var e in n)ze(t,e,{get:n[e],enumerable:!0})},Qe=(t,n,e,r)=>{if(n&&typeof n=="object"||typeof n=="function")for(let o of Nt(n))!Qt.call(t,o)&&o!==e&&ze(t,o,{get:()=>n[o],enumerable:!(r=Dt(n,o))||r.enumerable});return t},E=(t,n,e)=>(Qe(t,n,"default"),e&&Qe(e,n,"default")),St=(t,n,e)=>(e=t!=null?jt(qt(t)):{},Qe(n||!t||!t.__esModule?ze(e,"default",{value:t,enumerable:!0}):e,t)),Gt=t=>Qe(ze({},"__esModule",{value:!0}),t);var U={};zt(U,{BailSignal:()=>ve,Controller:()=>pe,FrameValue:()=>te,Globals:()=>be.Globals,Interpolation:()=>oe,Spring:()=>cn,SpringContext:()=>Ve,SpringRef:()=>le,SpringValue:()=>ue,Trail:()=>fn,Transition:()=>dn,config:()=>st,createInterpolator:()=>fe.createInterpolator,easings:()=>fe.easings,inferTo:()=>ye,interpolate:()=>gn,to:()=>hn,update:()=>yn,useChain:()=>Yt,useInView:()=>ln,useIsomorphicLayoutEffect:()=>fe.useIsomorphicLayoutEffect,useReducedMotion:()=>fe.useReducedMotion,useResize:()=>un,useScroll:()=>an,useSpring:()=>re,useSpringRef:()=>nn,useSpringValue:()=>rn,useSprings:()=>je,useTrail:()=>mt,useTransition:()=>ht});module.exports=Gt(U);var Z=require("@react-spring/shared");var _=require("@react-spring/shared");function k(t,...n){return _.is.fun(t)?t(...n):t}var he=(t,n)=>t===!0||!!(n&&t&&(_.is.fun(t)?t(n):(0,_.toArray)(t).includes(n))),ot=(t,n)=>_.is.obj(t)?n&&t[n]:t;var Ge=(t,n)=>t.default===!0?t[n]:t.default?t.default[n]:void 0,Xt=t=>t,ge=(t,n=Xt)=>{let e=Bt;t.default&&t.default!==!0&&(t=t.default,e=Object.keys(t));let r={};for(let o of e){let s=n(t[o],o);_.is.und(s)||(r[o]=s)}return r},Bt=["config","onProps","onStart","onChange","onPause","onResume","onRest"],Kt={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function Jt(t){let n={},e=0;if((0,_.eachProp)(t,(r,o)=>{Kt[o]||(n[o]=r,e++)}),e)return n}function ye(t){let n=Jt(t);if(n){let e={to:n};return(0,_.eachProp)(t,(r,o)=>o in n||(e[o]=r)),e}return{...t}}function Ae(t){return t=(0,_.getFluidValue)(t),_.is.arr(t)?t.map(Ae):(0,_.isAnimatedString)(t)?_.Globals.createStringInterpolator({range:[0,1],output:[t,t]})(1):t}function Xe(t){for(let n in t)return!0;return!1}function Be(t){return _.is.fun(t)||_.is.arr(t)&&_.is.obj(t[0])}function ke(t,n){t.ref?.delete(t),n?.delete(t)}function Re(t,n){n&&t.ref!==n&&(t.ref?.delete(t),n.add(t),t.ref=n)}function Yt(t,n,e=1e3){(0,Z.useIsomorphicLayoutEffect)(()=>{if(n){let r=0;(0,Z.each)(t,(o,s)=>{let u=o.current;if(u.length){let i=e*n[s];isNaN(i)?i=r:r=i,(0,Z.each)(u,l=>{(0,Z.each)(l.queue,p=>{let R=p.delay;p.delay=c=>i+k(R||0,c)})}),o.start()}})}else{let r=Promise.resolve();(0,Z.each)(t,o=>{let s=o.current;if(s.length){let u=s.map(i=>{let l=i.queue;return i.queue=[],l});r=r.then(()=>((0,Z.each)(s,(i,l)=>(0,Z.each)(u[l]||[],p=>i.queue.push(p))),Promise.all(o.start())))}})}})}var Ft=require("@react-spring/shared");var H=require("react"),j=require("@react-spring/shared");var a=require("@react-spring/shared"),O=require("@react-spring/animated");var W=require("@react-spring/shared");var st={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}};var it={...st.default,mass:1,damping:1,easing:W.easings.linear,clamp:!1},Ke=class{constructor(){this.velocity=0;Object.assign(this,it)}};function Tt(t,n,e){e&&(e={...e},Pt(e,n),n={...e,...n}),Pt(t,n),Object.assign(t,n);for(let u in it)t[u]==null&&(t[u]=it[u]);let{frequency:r,damping:o}=t,{mass:s}=t;return W.is.und(r)||(r<.01&&(r=.01),o<0&&(o=0),t.tension=Math.pow(2*Math.PI/r,2)*s,t.friction=4*Math.PI*o*s/r),t}function Pt(t,n){if(!W.is.und(n.decay))t.duration=void 0;else{let e=!W.is.und(n.tension)||!W.is.und(n.friction);(e||!W.is.und(n.frequency)||!W.is.und(n.damping)||!W.is.und(n.mass))&&(t.duration=void 0,t.decay=void 0),e&&(t.frequency=void 0)}}var xt=[],Je=class{constructor(){this.changed=!1;this.values=xt;this.toValues=null;this.fromValues=xt;this.config=new Ke;this.immediate=!1}};var Se=require("@react-spring/shared");function Ye(t,{key:n,props:e,defaultProps:r,state:o,actions:s}){return new Promise((u,i)=>{let l,p,R=he(e.cancel??r?.cancel,n);if(R)y();else{Se.is.und(e.pause)||(o.paused=he(e.pause,n));let g=r?.pause;g!==!0&&(g=o.paused||he(g,n)),l=k(e.delay||0,n),g?(o.resumeQueue.add(S),s.pause()):(s.resume(),S())}function c(){o.resumeQueue.add(S),o.timeouts.delete(p),p.cancel(),l=p.time-Se.raf.now()}function S(){l>0&&!Se.Globals.skipAnimation?(o.delayed=!0,p=Se.raf.setTimeout(y,l),o.pauseQueue.add(c),o.timeouts.add(p)):y()}function y(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(c),o.timeouts.delete(p),t<=(o.cancelId||0)&&(R=!0);try{s.start({...e,callId:t,cancel:R},u)}catch(g){i(g)}}})}var Q=require("@react-spring/shared");var Ue=(t,n)=>n.length==1?n[0]:n.some(e=>e.cancelled)?ee(t.get()):n.every(e=>e.noop)?at(t.get()):X(t.get(),n.every(e=>e.finished)),at=t=>({value:t,noop:!0,finished:!0,cancelled:!1}),X=(t,n,e=!1)=>({value:t,finished:n,cancelled:e}),ee=t=>({value:t,cancelled:!0,finished:!1});function We(t,n,e,r){let{callId:o,parentId:s,onRest:u}=n,{asyncTo:i,promise:l}=e;return!s&&t===i&&!n.reset?l:e.promise=(async()=>{e.asyncId=o,e.asyncTo=t;let p=ge(n,(m,d)=>d==="onRest"?void 0:m),R,c,S=new Promise((m,d)=>(R=m,c=d)),y=m=>{let d=o<=(e.cancelId||0)&&ee(r)||o!==e.asyncId&&X(r,!1);if(d)throw m.result=d,c(m),m},g=(m,d)=>{let b=new ve,T=new He;return(async()=>{if(Q.Globals.skipAnimation)throw Pe(e),T.result=X(r,!1),c(T),T;y(b);let I=Q.is.obj(m)?{...m}:{...d,to:m};I.parentId=o,(0,Q.eachProp)(p,(F,V)=>{Q.is.und(I[V])&&(I[V]=F)});let A=await r.start(I);return y(b),e.paused&&await new Promise(F=>{e.resumeQueue.add(F)}),A})()},f;if(Q.Globals.skipAnimation)return Pe(e),X(r,!1);try{let m;Q.is.arr(t)?m=(async d=>{for(let b of d)await g(b)})(t):m=Promise.resolve(t(g,r.stop.bind(r))),await Promise.all([m.then(R),S]),f=X(r.get(),!0,!1)}catch(m){if(m instanceof ve)f=m.result;else if(m instanceof He)f=m.result;else throw m}finally{o==e.asyncId&&(e.asyncId=s,e.asyncTo=s?i:void 0,e.promise=s?l:void 0)}return Q.is.fun(u)&&Q.raf.batchedUpdates(()=>{u(f,r,r.item)}),f})()}function Pe(t,n){(0,Q.flush)(t.timeouts,e=>e.cancel()),t.pauseQueue.clear(),t.resumeQueue.clear(),t.asyncId=t.asyncTo=t.promise=void 0,n&&(t.cancelId=n)}var ve=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},He=class extends Error{constructor(){super("SkipAnimationSignal")}};var K=require("@react-spring/shared"),bt=require("@react-spring/animated"),Ee=t=>t instanceof te,Ht=1,te=class extends K.FluidValue{constructor(){super(...arguments);this.id=Ht++;this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=(0,bt.getAnimated)(this);return e&&e.getValue()}to(...e){return K.Globals.to(this,e)}interpolate(...e){return(0,K.deprecateInterpolate)(),K.Globals.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,r=!1){(0,K.callFluidObservers)(this,{type:"change",parent:this,value:e,idle:r})}_onPriorityChange(e){this.idle||K.frameLoop.sort(this),(0,K.callFluidObservers)(this,{type:"priority",parent:this,priority:e})}};var Te=Symbol.for("SpringPhase"),At=1,ut=2,pt=4,$e=t=>(t[Te]&At)>0,ne=t=>(t[Te]&ut)>0,Ce=t=>(t[Te]&pt)>0,lt=(t,n)=>n?t[Te]|=ut|At:t[Te]&=~ut,ct=(t,n)=>n?t[Te]|=pt:t[Te]&=~pt;var ue=class extends te{constructor(e,r){super();this.animation=new Je;this.defaultProps={};this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._pendingCalls=new Set;this._lastCallId=0;this._lastToId=0;this._memoizedDuration=0;if(!a.is.und(e)||!a.is.und(r)){let o=a.is.obj(e)?{...e}:{...r,from:e};a.is.und(o.default)&&(o.default=!0),this.start(o)}}get idle(){return!(ne(this)||this._state.asyncTo)||Ce(this)}get goal(){return(0,a.getFluidValue)(this.animation.to)}get velocity(){let e=(0,O.getAnimated)(this);return e instanceof O.AnimatedValue?e.lastVelocity||0:e.getPayload().map(r=>r.lastVelocity||0)}get hasAnimated(){return $e(this)}get isAnimating(){return ne(this)}get isPaused(){return Ce(this)}get isDelayed(){return this._state.delayed}advance(e){let r=!0,o=!1,s=this.animation,{toValues:u}=s,{config:i}=s,l=(0,O.getPayload)(s.to);!l&&(0,a.hasFluidValue)(s.to)&&(u=(0,a.toArray)((0,a.getFluidValue)(s.to))),s.values.forEach((c,S)=>{if(c.done)return;let y=c.constructor==O.AnimatedString?1:l?l[S].lastPosition:u[S],g=s.immediate,f=y;if(!g){if(f=c.lastPosition,i.tension<=0){c.done=!0;return}let m=c.elapsedTime+=e,d=s.fromValues[S],b=c.v0!=null?c.v0:c.v0=a.is.arr(i.velocity)?i.velocity[S]:i.velocity,T,I=i.precision||(d==y?.005:Math.min(1,Math.abs(y-d)*.001));if(a.is.und(i.duration))if(i.decay){let A=i.decay===!0?.998:i.decay,F=Math.exp(-(1-A)*m);f=d+b/(1-A)*(1-F),g=Math.abs(c.lastPosition-f)<=I,T=b*F}else{T=c.lastVelocity==null?b:c.lastVelocity;let A=i.restVelocity||I/10,F=i.clamp?0:i.bounce,V=!a.is.und(F),z=d==y?c.v0>0:d<y,w,J=!1,q=1,ie=Math.ceil(e/q);for(let B=0;B<ie&&(w=Math.abs(T)>A,!(!w&&(g=Math.abs(y-f)<=I,g)));++B){V&&(J=f==y||f>y==z,J&&(T=-T*F,f=y));let h=-i.tension*1e-6*(f-y),x=-i.friction*.001*T,L=(h+x)/i.mass;T=T+L*q,f=f+T*q}}else{let A=1;i.duration>0&&(this._memoizedDuration!==i.duration&&(this._memoizedDuration=i.duration,c.durationProgress>0&&(c.elapsedTime=i.duration*c.durationProgress,m=c.elapsedTime+=e)),A=(i.progress||0)+m/this._memoizedDuration,A=A>1?1:A<0?0:A,c.durationProgress=A),f=d+i.easing(A)*(y-d),T=(f-c.lastPosition)/e,g=A==1}c.lastVelocity=T,Number.isNaN(f)&&(console.warn("Got NaN while animating:",this),g=!0)}l&&!l[S].done&&(g=!1),g?c.done=!0:r=!1,c.setValue(f,i.round)&&(o=!0)});let p=(0,O.getAnimated)(this),R=p.getValue();if(r){let c=(0,a.getFluidValue)(s.to);(R!==c||o)&&!i.decay?(p.setValue(c),this._onChange(c)):o&&i.decay&&this._onChange(R),this._stop()}else o&&this._onChange(R)}set(e){return a.raf.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(ne(this)){let{to:e,config:r}=this.animation;a.raf.batchedUpdates(()=>{this._onStart(),r.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,r){let o;return a.is.und(e)?(o=this.queue||[],this.queue=[]):o=[a.is.obj(e)?e:{...r,to:e}],Promise.all(o.map(s=>this._update(s))).then(s=>Ue(this,s))}stop(e){let{to:r}=this.animation;return this._focus(this.get()),Pe(this._state,e&&this._lastCallId),a.raf.batchedUpdates(()=>this._stop(r,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){let r=this.key||"",{to:o,from:s}=e;o=a.is.obj(o)?o[r]:o,(o==null||Be(o))&&(o=void 0),s=a.is.obj(s)?s[r]:s,s==null&&(s=void 0);let u={to:o,from:s};return $e(this)||(e.reverse&&([o,s]=[s,o]),s=(0,a.getFluidValue)(s),a.is.und(s)?(0,O.getAnimated)(this)||this._set(o):this._set(s)),u}_update({...e},r){let{key:o,defaultProps:s}=this;e.default&&Object.assign(s,ge(e,(l,p)=>/^on/.test(p)?ot(l,o):l)),vt(this,e,"onProps"),Le(this,"onProps",e,this);let u=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let i=this._state;return Ye(++this._lastCallId,{key:o,props:e,defaultProps:s,state:i,actions:{pause:()=>{Ce(this)||(ct(this,!0),(0,a.flushCalls)(i.pauseQueue),Le(this,"onPause",X(this,we(this,this.animation.to)),this))},resume:()=>{Ce(this)&&(ct(this,!1),ne(this)&&this._resume(),(0,a.flushCalls)(i.resumeQueue),Le(this,"onResume",X(this,we(this,this.animation.to)),this))},start:this._merge.bind(this,u)}}).then(l=>{if(e.loop&&l.finished&&!(r&&l.noop)){let p=ft(e);if(p)return this._update(p,!0)}return l})}_merge(e,r,o){if(r.cancel)return this.stop(!0),o(ee(this));let s=!a.is.und(e.to),u=!a.is.und(e.from);if(s||u)if(r.callId>this._lastToId)this._lastToId=r.callId;else return o(ee(this));let{key:i,defaultProps:l,animation:p}=this,{to:R,from:c}=p,{to:S=R,from:y=c}=e;u&&!s&&(!r.default||a.is.und(S))&&(S=y),r.reverse&&([S,y]=[y,S]);let g=!(0,a.isEqual)(y,c);g&&(p.from=y),y=(0,a.getFluidValue)(y);let f=!(0,a.isEqual)(S,R);f&&this._focus(S);let m=Be(r.to),{config:d}=p,{decay:b,velocity:T}=d;(s||u)&&(d.velocity=0),r.config&&!m&&Tt(d,k(r.config,i),r.config!==l.config?k(l.config,i):void 0);let I=(0,O.getAnimated)(this);if(!I||a.is.und(S))return o(X(this,!0));let A=a.is.und(r.reset)?u&&!r.default:!a.is.und(y)&&he(r.reset,i),F=A?y:this.get(),V=Ae(S),z=a.is.num(V)||a.is.arr(V)||(0,a.isAnimatedString)(V),w=!m&&(!z||he(l.immediate||r.immediate,i));if(f){let B=(0,O.getAnimatedType)(S);if(B!==I.constructor)if(w)I=this._set(V);else throw Error(`Cannot animate between ${I.constructor.name} and ${B.name}, as the "to" prop suggests`)}let J=I.constructor,q=(0,a.hasFluidValue)(S),ie=!1;if(!q){let B=A||!$e(this)&&g;(f||B)&&(ie=(0,a.isEqual)(Ae(F),V),q=!ie),(!(0,a.isEqual)(p.immediate,w)&&!w||!(0,a.isEqual)(d.decay,b)||!(0,a.isEqual)(d.velocity,T))&&(q=!0)}if(ie&&ne(this)&&(p.changed&&!A?q=!0:q||this._stop(R)),!m&&((q||(0,a.hasFluidValue)(R))&&(p.values=I.getPayload(),p.toValues=(0,a.hasFluidValue)(S)?null:J==O.AnimatedString?[1]:(0,a.toArray)(V)),p.immediate!=w&&(p.immediate=w,!w&&!A&&this._set(R)),q)){let{onRest:B}=p;(0,a.each)(Wt,x=>vt(this,r,x));let h=X(this,we(this,R));(0,a.flushCalls)(this._pendingCalls,h),this._pendingCalls.add(o),p.changed&&a.raf.batchedUpdates(()=>{p.changed=!A,B?.(h,this),A?k(l.onRest,h):p.onStart?.(h,this)})}A&&this._set(F),m?o(We(r.to,r,this._state,this)):q?this._start():ne(this)&&!f?this._pendingCalls.add(o):o(at(F))}_focus(e){let r=this.animation;e!==r.to&&((0,a.getFluidObservers)(this)&&this._detach(),r.to=e,(0,a.getFluidObservers)(this)&&this._attach())}_attach(){let e=0,{to:r}=this.animation;(0,a.hasFluidValue)(r)&&((0,a.addFluidObserver)(r,this),Ee(r)&&(e=r.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;(0,a.hasFluidValue)(e)&&(0,a.removeFluidObserver)(e,this)}_set(e,r=!0){let o=(0,a.getFluidValue)(e);if(!a.is.und(o)){let s=(0,O.getAnimated)(this);if(!s||!(0,a.isEqual)(o,s.getValue())){let u=(0,O.getAnimatedType)(o);!s||s.constructor!=u?(0,O.setAnimated)(this,u.create(o)):s.setValue(o),s&&a.raf.batchedUpdates(()=>{this._onChange(o,r)})}}return(0,O.getAnimated)(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,Le(this,"onStart",X(this,we(this,e.to)),this))}_onChange(e,r){r||(this._onStart(),k(this.animation.onChange,e,this)),k(this.defaultProps.onChange,e,this),super._onChange(e,r)}_start(){let e=this.animation;(0,O.getAnimated)(this).reset((0,a.getFluidValue)(e.to)),e.immediate||(e.fromValues=e.values.map(r=>r.lastPosition)),ne(this)||(lt(this,!0),Ce(this)||this._resume())}_resume(){a.Globals.skipAnimation?this.finish():a.frameLoop.start(this)}_stop(e,r){if(ne(this)){lt(this,!1);let o=this.animation;(0,a.each)(o.values,u=>{u.done=!0}),o.toValues&&(o.onChange=o.onPause=o.onResume=void 0),(0,a.callFluidObservers)(this,{type:"idle",parent:this});let s=r?ee(this.get()):X(this.get(),we(this,e??o.to));(0,a.flushCalls)(this._pendingCalls,s),o.changed&&(o.changed=!1,Le(this,"onRest",s,this))}}};function we(t,n){let e=Ae(n),r=Ae(t.get());return(0,a.isEqual)(r,e)}function ft(t,n=t.loop,e=t.to){let r=k(n);if(r){let o=r!==!0&&ye(r),s=(o||t).reverse,u=!o||o.reset;return Ie({...t,loop:n,default:!1,pause:void 0,to:!s||Be(e)?e:void 0,from:u?t.from:void 0,reset:u,...o})}}function Ie(t){let{to:n,from:e}=t=ye(t),r=new Set;return a.is.obj(n)&&Rt(n,r),a.is.obj(e)&&Rt(e,r),t.keys=r.size?Array.from(r):null,t}function Ct(t){let n=Ie(t);return a.is.und(n.default)&&(n.default=ge(n)),n}function Rt(t,n){(0,a.eachProp)(t,(e,r)=>e!=null&&n.add(r))}var Wt=["onStart","onRest","onChange","onPause","onResume"];function vt(t,n,e){t.animation[e]=n[e]!==Ge(n,e)?ot(n[e],t.key):void 0}function Le(t,n,...e){t.animation[n]?.(...e),t.defaultProps[n]?.(...e)}var P=require("@react-spring/shared");var $t=["onStart","onChange","onRest"],Zt=1,pe=class{constructor(n,e){this.id=Zt++;this.springs={};this.queue=[];this._lastAsyncId=0;this._active=new Set;this._changed=new Set;this._started=!1;this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._events={onStart:new Map,onChange:new Map,onRest:new Map};this._onFrame=this._onFrame.bind(this),e&&(this._flush=e),n&&this.start({default:!0,...n})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(n=>n.idle&&!n.isDelayed&&!n.isPaused)}get item(){return this._item}set item(n){this._item=n}get(){let n={};return this.each((e,r)=>n[r]=e.get()),n}set(n){for(let e in n){let r=n[e];P.is.und(r)||this.springs[e].set(r)}}update(n){return n&&this.queue.push(Ie(n)),this}start(n){let{queue:e}=this;return n?e=(0,P.toArray)(n).map(Ie):this.queue=[],this._flush?this._flush(this,e):(Ot(this,e),Ze(this,e))}stop(n,e){if(n!==!!n&&(e=n),e){let r=this.springs;(0,P.each)((0,P.toArray)(e),o=>r[o].stop(!!n))}else Pe(this._state,this._lastAsyncId),this.each(r=>r.stop(!!n));return this}pause(n){if(P.is.und(n))this.start({pause:!0});else{let e=this.springs;(0,P.each)((0,P.toArray)(n),r=>e[r].pause())}return this}resume(n){if(P.is.und(n))this.start({pause:!1});else{let e=this.springs;(0,P.each)((0,P.toArray)(n),r=>e[r].resume())}return this}each(n){(0,P.eachProp)(this.springs,n)}_onFrame(){let{onStart:n,onChange:e,onRest:r}=this._events,o=this._active.size>0,s=this._changed.size>0;(o&&!this._started||s&&!this._started)&&(this._started=!0,(0,P.flush)(n,([l,p])=>{p.value=this.get(),l(p,this,this._item)}));let u=!o&&this._started,i=s||u&&r.size?this.get():null;s&&e.size&&(0,P.flush)(e,([l,p])=>{p.value=i,l(p,this,this._item)}),u&&(this._started=!1,(0,P.flush)(r,([l,p])=>{p.value=i,l(p,this,this._item)}))}eventObserved(n){if(n.type=="change")this._changed.add(n.parent),n.idle||this._active.add(n.parent);else if(n.type=="idle")this._active.delete(n.parent);else return;P.raf.onFrame(this._onFrame)}};function Ze(t,n){return Promise.all(n.map(e=>It(t,e))).then(e=>Ue(t,e))}async function It(t,n,e){let{keys:r,to:o,from:s,loop:u,onRest:i,onResolve:l}=n,p=P.is.obj(n.default)&&n.default;u&&(n.loop=!1),o===!1&&(n.to=null),s===!1&&(n.from=null);let R=P.is.arr(o)||P.is.fun(o)?o:void 0;R?(n.to=void 0,n.onRest=void 0,p&&(p.onRest=void 0)):(0,P.each)($t,f=>{let m=n[f];if(P.is.fun(m)){let d=t._events[f];n[f]=({finished:b,cancelled:T})=>{let I=d.get(m);I?(b||(I.finished=!1),T&&(I.cancelled=!0)):d.set(m,{value:null,finished:b||!1,cancelled:T||!1})},p&&(p[f]=n[f])}});let c=t._state;n.pause===!c.paused?(c.paused=n.pause,(0,P.flushCalls)(n.pause?c.pauseQueue:c.resumeQueue)):c.paused&&(n.pause=!0);let S=(r||Object.keys(t.springs)).map(f=>t.springs[f].start(n)),y=n.cancel===!0||Ge(n,"cancel")===!0;(R||y&&c.asyncId)&&S.push(Ye(++t._lastAsyncId,{props:n,state:c,actions:{pause:P.noop,resume:P.noop,start(f,m){y?(Pe(c,t._lastAsyncId),m(ee(t))):(f.onRest=i,m(We(R,f,c,t)))}}})),c.paused&&await new Promise(f=>{c.resumeQueue.add(f)});let g=Ue(t,await Promise.all(S));if(u&&g.finished&&!(e&&g.noop)){let f=ft(n,u,o);if(f)return Ot(t,[f]),It(t,f,!0)}return l&&P.raf.batchedUpdates(()=>l(g,t,t.item)),g}function Me(t,n){let e={...t.springs};return n&&(0,P.each)((0,P.toArray)(n),r=>{P.is.und(r.keys)&&(r=Ie(r)),P.is.obj(r.to)||(r={...r,to:void 0}),_t(e,r,o=>Vt(o))}),dt(t,e),e}function dt(t,n){(0,P.eachProp)(n,(e,r)=>{t.springs[r]||(t.springs[r]=e,(0,P.addFluidObserver)(e,t))})}function Vt(t,n){let e=new ue;return e.key=t,n&&(0,P.addFluidObserver)(e,n),e}function _t(t,n,e){n.keys&&(0,P.each)(n.keys,r=>{(t[r]||(t[r]=e(r)))._prepareNode(n)})}function Ot(t,n){(0,P.each)(n,e=>{_t(t.springs,e,r=>Vt(r,t))})}var et=St(require("react")),en=require("react"),Ve=et.createContext({pause:!1,immediate:!1});var N=require("@react-spring/shared"),le=()=>{let t=[],n=function(r){(0,N.deprecateDirectCall)();let o=[];return(0,N.each)(t,(s,u)=>{if(N.is.und(r))o.push(s.start());else{let i=e(r,s,u);i&&o.push(s.start(i))}}),o};n.current=t,n.add=function(r){t.includes(r)||t.push(r)},n.delete=function(r){let o=t.indexOf(r);~o&&t.splice(o,1)},n.pause=function(){return(0,N.each)(t,r=>r.pause(...arguments)),this},n.resume=function(){return(0,N.each)(t,r=>r.resume(...arguments)),this},n.set=function(r){(0,N.each)(t,(o,s)=>{let u=N.is.fun(r)?r(s,o):r;u&&o.set(u)})},n.start=function(r){let o=[];return(0,N.each)(t,(s,u)=>{if(N.is.und(r))o.push(s.start());else{let i=this._getProps(r,s,u);i&&o.push(s.start(i))}}),o},n.stop=function(){return(0,N.each)(t,r=>r.stop(...arguments)),this},n.update=function(r){return(0,N.each)(t,(o,s)=>o.update(this._getProps(r,o,s))),this};let e=function(r,o,s){return N.is.fun(r)?r(s,o):r};return n._getProps=e,n};function je(t,n,e){let r=j.is.fun(n)&&n;r&&!e&&(e=[]);let o=(0,H.useMemo)(()=>r||arguments.length==3?le():void 0,[]),s=(0,H.useRef)(0),u=(0,j.useForceUpdate)(),i=(0,H.useMemo)(()=>({ctrls:[],queue:[],flush(d,b){let T=Me(d,b);return s.current>0&&!i.queue.length&&!Object.keys(T).some(A=>!d.springs[A])?Ze(d,b):new Promise(A=>{dt(d,T),i.queue.push(()=>{A(Ze(d,b))}),u()})}}),[]),l=(0,H.useRef)([...i.ctrls]),p=(0,H.useRef)([]),R=(0,j.usePrev)(t)||0;(0,H.useMemo)(()=>{(0,j.each)(l.current.slice(t,R),d=>{ke(d,o),d.stop(!0)}),l.current.length=t,c(R,t)},[t]),(0,H.useMemo)(()=>{c(0,Math.min(R,t))},e);function c(d,b){for(let T=d;T<b;T++){let I=l.current[T]||(l.current[T]=new pe(null,i.flush)),A=r?r(T,I):n[T];A&&(p.current[T]=Ct(A))}}let S=l.current.map((d,b)=>Me(d,p.current[b])),y=(0,H.useContext)(Ve),g=(0,j.usePrev)(y),f=y!==g&&Xe(y);(0,j.useIsomorphicLayoutEffect)(()=>{s.current++,i.ctrls=l.current;let{queue:d}=i;d.length&&(i.queue=[],(0,j.each)(d,b=>b())),(0,j.each)(l.current,(b,T)=>{o?.add(b),f&&b.start({default:y});let I=p.current[T];I&&(Re(b,I.ref),b.ref?b.queue.push(I):b.start(I))})}),(0,j.useOnce)(()=>()=>{(0,j.each)(i.ctrls,d=>d.stop(!0))});let m=S.map(d=>({...d}));return o?[m,o]:m}function re(t,n){let e=Ft.is.fun(t),[[r],o]=je(1,e?t:[t],e?n||[]:n);return e||arguments.length==2?[r,o]:r}var kt=require("react");var tn=()=>le(),nn=()=>(0,kt.useState)(tn)[0];var tt=require("@react-spring/shared");var rn=(t,n)=>{let e=(0,tt.useConstant)(()=>new ue(t,n));return(0,tt.useOnce)(()=>()=>{e.stop()}),e};var xe=require("@react-spring/shared");function mt(t,n,e){let r=xe.is.fun(n)&&n;r&&!e&&(e=[]);let o=!0,s,u=je(t,(i,l)=>{let p=r?r(i,l):n;return s=p.ref,o=o&&p.reverse,p},e||[{}]);if((0,xe.useIsomorphicLayoutEffect)(()=>{(0,xe.each)(u[1].current,(i,l)=>{let p=u[1].current[l+(o?1:-1)];if(Re(i,s),i.ref){p&&i.update({to:p.springs});return}p?i.start({to:p.springs}):i.start()})},e),r||arguments.length==3){let i=s??u[1];return i._getProps=(l,p,R)=>{let c=xe.is.fun(l)?l(R,p):l;if(c){let S=i.current[R+(c.reverse?1:-1)];return S&&(c.to=S.springs),c}},u}return u[0]}var De=St(require("react")),ce=require("react"),v=require("@react-spring/shared");function ht(t,n,e){let r=v.is.fun(n)&&n,{reset:o,sort:s,trail:u=0,expires:i=!0,exitBeforeEnter:l=!1,onDestroyed:p,ref:R,config:c}=r?r():n,S=(0,ce.useMemo)(()=>r||arguments.length==3?le():void 0,[]),y=(0,v.toArray)(t),g=[],f=(0,ce.useRef)(null),m=o?null:f.current;(0,v.useIsomorphicLayoutEffect)(()=>{f.current=g}),(0,v.useOnce)(()=>((0,v.each)(g,h=>{S?.add(h.ctrl),h.ctrl.ref=S}),()=>{(0,v.each)(f.current,h=>{h.expired&&clearTimeout(h.expirationId),ke(h.ctrl,S),h.ctrl.stop(!0)})}));let d=sn(y,r?r():n,m),b=o&&f.current||[];(0,v.useIsomorphicLayoutEffect)(()=>(0,v.each)(b,({ctrl:h,item:x,key:L})=>{ke(h,S),k(p,x,L)}));let T=[];if(m&&(0,v.each)(m,(h,x)=>{h.expired?(clearTimeout(h.expirationId),b.push(h)):(x=T[x]=d.indexOf(h.key),~x&&(g[x]=h))}),(0,v.each)(y,(h,x)=>{g[x]||(g[x]={key:d[x],item:h,phase:"mount",ctrl:new pe},g[x].ctrl.item=h)}),T.length){let h=-1,{leave:x}=r?r():n;(0,v.each)(T,(L,D)=>{let M=m[D];~L?(h=g.indexOf(M),g[h]={...M,item:y[L]}):x&&g.splice(++h,0,M)})}v.is.fun(s)&&g.sort((h,x)=>s(h.item,x.item));let I=-u,A=(0,v.useForceUpdate)(),F=ge(n),V=new Map,z=(0,ce.useRef)(new Map),w=(0,ce.useRef)(!1);(0,v.each)(g,(h,x)=>{let L=h.key,D=h.phase,M=r?r():n,G,$,Lt=k(M.delay||0,L);if(D=="mount")G=M.enter,$="enter";else{let Y=d.indexOf(L)<0;if(D!="leave")if(Y)G=M.leave,$="leave";else if(G=M.update)$="update";else return;else if(!Y)G=M.enter,$="enter";else return}if(G=k(G,h.item,x),G=v.is.obj(G)?ye(G):{to:G},!G.config){let Y=c||F.config;G.config=k(Y,h.item,x,$)}I+=u;let de={...F,delay:Lt+I,ref:R,immediate:M.immediate,reset:!1,...G};if($=="enter"&&v.is.und(de.from)){let Y=r?r():n,Fe=v.is.und(Y.initial)||m?Y.from:Y.initial;de.from=k(Fe,h.item,x)}let{onResolve:Mt}=de;de.onResolve=Y=>{k(Mt,Y);let Fe=f.current,ae=Fe.find(qe=>qe.key===L);if(ae&&!(Y.cancelled&&ae.phase!="update")&&ae.ctrl.idle){let qe=Fe.every(me=>me.ctrl.idle);if(ae.phase=="leave"){let me=k(i,ae.item);if(me!==!1){let rt=me===!0?0:me;if(ae.expired=!0,!qe&&rt>0){rt<=2147483647&&(ae.expirationId=setTimeout(A,rt));return}}}qe&&Fe.some(me=>me.expired)&&(z.current.delete(ae),l&&(w.current=!0),A())}};let yt=Me(h.ctrl,de);$==="leave"&&l?z.current.set(h,{phase:$,springs:yt,payload:de}):V.set(h,{phase:$,springs:yt,payload:de})});let J=(0,ce.useContext)(Ve),q=(0,v.usePrev)(J),ie=J!==q&&Xe(J);(0,v.useIsomorphicLayoutEffect)(()=>{ie&&(0,v.each)(g,h=>{h.ctrl.start({default:J})})},[J]),(0,v.each)(V,(h,x)=>{if(z.current.size){let L=g.findIndex(D=>D.key===x.key);g.splice(L,1)}}),(0,v.useIsomorphicLayoutEffect)(()=>{(0,v.each)(z.current.size?z.current:V,({phase:h,payload:x},L)=>{let{ctrl:D}=L;L.phase=h,S?.add(D),ie&&h=="enter"&&D.start({default:J}),x&&(Re(D,x.ref),(D.ref||S)&&!w.current?D.update(x):(D.start(x),w.current&&(w.current=!1)))})},o?void 0:e);let B=h=>De.createElement(De.Fragment,null,g.map((x,L)=>{let{springs:D}=V.get(x)||x.ctrl,M=h({...D},x.item,x,L);return M&&M.type?De.createElement(M.type,{...M.props,key:v.is.str(x.key)||v.is.num(x.key)?x.key:x.ctrl.id,ref:M.ref}):M}));return S?[B,S]:B}var on=1;function sn(t,{key:n,keys:e=n},r){if(e===null){let o=new Set;return t.map(s=>{let u=r&&r.find(i=>i.item===s&&i.phase!=="leave"&&!o.has(i));return u?(o.add(u),u.key):on++})}return v.is.und(e)?t:v.is.fun(e)?t.map(e):(0,v.toArray)(e)}var _e=require("@react-spring/shared");var an=({container:t,...n}={})=>{let[e,r]=re(()=>({scrollX:0,scrollY:0,scrollXProgress:0,scrollYProgress:0,...n}),[]);return(0,_e.useIsomorphicLayoutEffect)(()=>{let o=(0,_e.onScroll)(({x:s,y:u})=>{r.start({scrollX:s.current,scrollXProgress:s.progress,scrollY:u.current,scrollYProgress:u.progress})},{container:t?.current||void 0});return()=>{(0,_e.each)(Object.values(e),s=>s.stop()),o()}},[]),e};var Oe=require("@react-spring/shared");var un=({container:t,...n})=>{let[e,r]=re(()=>({width:0,height:0,...n}),[]);return(0,Oe.useIsomorphicLayoutEffect)(()=>{let o=(0,Oe.onResize)(({width:s,height:u})=>{r.start({width:s,height:u,immediate:e.width.get()===0||e.height.get()===0})},{container:t?.current||void 0});return()=>{(0,Oe.each)(Object.values(e),s=>s.stop()),o()}},[]),e};var nt=require("react"),Ne=require("@react-spring/shared");var pn={any:0,all:1};function ln(t,n){let[e,r]=(0,nt.useState)(!1),o=(0,nt.useRef)(void 0),s=Ne.is.fun(t)&&t,u=s?s():{},{to:i={},from:l={},...p}=u,R=s?n:t,[c,S]=re(()=>({from:l,...p}),[]);return(0,Ne.useIsomorphicLayoutEffect)(()=>{let y=o.current,{root:g,once:f,amount:m="any",...d}=R??{};if(!y||f&&e||typeof IntersectionObserver>"u")return;let b=new WeakMap,T=()=>(i&&S.start(i),r(!0),f?void 0:()=>{l&&S.start(l),r(!1)}),I=F=>{F.forEach(V=>{let z=b.get(V.target);if(V.isIntersecting!==!!z)if(V.isIntersecting){let w=T();Ne.is.fun(w)?b.set(V.target,w):A.unobserve(V.target)}else z&&(z(),b.delete(V.target))})},A=new IntersectionObserver(I,{root:g&&g.current||void 0,threshold:typeof m=="number"||Array.isArray(m)?m:pn[m],...d});return A.observe(y),()=>A.unobserve(y)},[R]),s?[o,c]:[o,e]}function cn({children:t,...n}){return t(re(n))}var Ut=require("@react-spring/shared");function fn({items:t,children:n,...e}){let r=mt(t.length,e);return t.map((o,s)=>{let u=n(o,s);return Ut.is.fun(u)?u(r[s]):u})}function dn({items:t,children:n,...e}){return ht(t,e)(n)}var wt=require("@react-spring/shared");var C=require("@react-spring/shared");var se=require("@react-spring/animated"),oe=class extends te{constructor(e,r){super();this.source=e;this.idle=!0;this._active=new Set;this.calc=(0,C.createInterpolator)(...r);let o=this._get(),s=(0,se.getAnimatedType)(o);(0,se.setAnimated)(this,s.create(o))}advance(e){let r=this._get(),o=this.get();(0,C.isEqual)(r,o)||((0,se.getAnimated)(this).setValue(r),this._onChange(r,this.idle)),!this.idle&&Et(this._active)&&gt(this)}_get(){let e=C.is.arr(this.source)?this.source.map(C.getFluidValue):(0,C.toArray)((0,C.getFluidValue)(this.source));return this.calc(...e)}_start(){this.idle&&!Et(this._active)&&(this.idle=!1,(0,C.each)((0,se.getPayload)(this),e=>{e.done=!1}),C.Globals.skipAnimation?(C.raf.batchedUpdates(()=>this.advance()),gt(this)):C.frameLoop.start(this))}_attach(){let e=1;(0,C.each)((0,C.toArray)(this.source),r=>{(0,C.hasFluidValue)(r)&&(0,C.addFluidObserver)(r,this),Ee(r)&&(r.idle||this._active.add(r),e=Math.max(e,r.priority+1))}),this.priority=e,this._start()}_detach(){(0,C.each)((0,C.toArray)(this.source),e=>{(0,C.hasFluidValue)(e)&&(0,C.removeFluidObserver)(e,this)}),this._active.clear(),gt(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=(0,C.toArray)(this.source).reduce((r,o)=>Math.max(r,(Ee(o)?o.priority:0)+1),0))}};function mn(t){return t.idle!==!1}function Et(t){return!t.size||Array.from(t).every(mn)}function gt(t){t.idle||(t.idle=!0,(0,C.each)((0,se.getPayload)(t),n=>{n.done=!0}),(0,C.callFluidObservers)(t,{type:"idle",parent:t}))}var hn=(t,...n)=>new oe(t,n),gn=(t,...n)=>((0,wt.deprecateInterpolate)(),new oe(t,n));var be=require("@react-spring/shared");be.Globals.assign({createStringInterpolator:be.createStringInterpolator,to:(t,n)=>new oe(t,n)});var yn=be.frameLoop.advance;var fe=require("@react-spring/shared");E(U,require("@react-spring/types"),module.exports);0&&(module.exports={BailSignal,Controller,FrameValue,Globals,Interpolation,Spring,SpringContext,SpringRef,SpringValue,Trail,Transition,config,createInterpolator,easings,inferTo,interpolate,to,update,useChain,useInView,useIsomorphicLayoutEffect,useReducedMotion,useResize,useScroll,useSpring,useSpringRef,useSpringValue,useSprings,useTrail,useTransition,...require("@react-spring/types")});
