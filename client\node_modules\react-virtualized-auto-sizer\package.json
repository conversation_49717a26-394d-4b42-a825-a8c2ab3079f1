{"name": "react-virtualized-auto-sizer", "version": "1.0.26", "description": "Standalone version of the AutoSizer component from react-virtualized", "author": "<PERSON> <<EMAIL>> (https://github.com/bvaughn/)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/bvaughn/)"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bvaughn/react-virtualized-auto-sizer.git"}, "keywords": ["react", "reactjs", "virtual", "window", "windowed", "list", "scrolling", "infinite", "virtualized", "table", "grid", "spreadsheet"], "main": "dist/react-virtualized-auto-sizer.cjs.js", "module": "dist/react-virtualized-auto-sizer.esm.js", "exports": {".": {"types": {"import": "./dist/react-virtualized-auto-sizer.cjs.mjs", "default": "./dist/react-virtualized-auto-sizer.cjs.js"}, "module": "./dist/react-virtualized-auto-sizer.esm.js", "import": "./dist/react-virtualized-auto-sizer.cjs.mjs", "default": "./dist/react-virtualized-auto-sizer.cjs.js"}, "./package.json": "./package.json"}, "types": "dist/react-virtualized-auto-sizer.cjs.d.ts", "files": ["dist"], "lint-staged": {"{example,src}/**/*.{js,json,css}": ["prettier --write", "git add"], "**/*.js": "eslint --max-warnings 0"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/helper-create-class-features-plugin": "^7.21.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/preset-typescript": "^7.21.5", "@preconstruct/cli": "^2.8.1", "@types/jest": "^26.0.15", "@types/react": "^18", "@types/react-dom": "^18", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^2.8.6", "react": "^18", "react-dom": "^18", "ts-jest": "^29.1.0", "typescript": "^4.1.2"}, "peerDependencies": {"react": "^15.3.0 || ^16.0.0-alpha || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^15.3.0 || ^16.0.0-alpha || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "preconstruct": {"exports": {"importConditionDefaultExport": "default"}, "___experimentalFlags_WILL_CHANGE_IN_PATCH": {"importsConditions": true}}, "scripts": {"clear": "npm run clear:builds & npm run clear:node_modules", "clear:builds": "rm -rf ./dist", "clear:node_modules": "rm -rf ./node_modules", "prerelease": "preconstruct build", "prettier": "prettier --write \"**/*.{css,html,js,json,jsx,ts,tsx}\"", "prettier:ci": "prettier --check \"**/*.{css,html,js,json,jsx,ts,tsx}\"", "test": "jest", "test:watch": "jest --watch", "typescript": "tsc --noEmit", "typescript:watch": "tsc --noEmit --watch"}, "readme": "# react-virtualized-auto-sizer\n\nStandalone version of the `AutoSizer` component from [`react-virtualized`](https://github.com/bvaughn/react-virtualized).\n\n### If you like this project, 🎉 [become a sponsor](https://github.com/sponsors/bvaughn/) or ☕ [buy me a coffee](http://givebrian.coffee/)\n\n## Install\n\n```bash\nnpm install --save react-virtualized-auto-sizer\n```\n\n## Documentation\n\n\n| Property      | Type     | Required? | Description                                                                                                                                                     |\n| :------------ | :------- | :-------: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n| children      | Function |     ✓     | Function responsible for rendering children. This function should implement the following signature: `({ height?: number \\| undefined, width?: number \\| undefined }) => PropTypes.element` |\n| className     | String   |           | Optional custom CSS class name to attach to root `AutoSizer` element. This is an advanced property and is not typically necessary.                              |\n| defaultHeight | Number   |           | Height passed to child for initial render; useful for server-side rendering. This value will be overridden with an accurate height after mounting.              |\n| defaultWidth  | Number   |           | Width passed to child for initial render; useful for server-side rendering. This value will be overridden with an accurate width after mounting.                |\n| disableHeight | Boolean  |           | Fixed `height`; if specified, the child's `height` property will not be managed                                                                                 |\n| disableWidth  | Boolean  |           | Fixed `width`; if specified, the child's `width` property will not be managed                                                                                   |\n| doNotBailOutOnEmptyChildren | boolean | | Optional propr that can override default behavior of not rendering children when either `width` or `height` are 0 |\n| nonce         | String   |           | Nonce of the inlined stylesheets for [Content Security Policy](https://www.w3.org/TR/2016/REC-CSP2-20161215/#script-src-the-nonce-attribute)                    |\n| onResize      | Function |           | Callback to be invoked on-resize; it is passed the following named parameters: `({ height: number, width: number })`.                                           |\n| style         | Object   |           | Optional custom inline style to attach to root `AutoSizer` element. This is an advanced property and is not typically necessary.                                |\n| tagName       | string   |           | Optional HTML tag name for root element; defaults to `\"div\"` |\n\n## Examples\n\nSome components (like those found in [`react-window`](https://github.com/bvaughn/react-window) or [`react-virtualized`](https://github.com/bvaughn/react-virtualized)) require numeric width and height parameters. The `AutoSizer` component can be useful if you want to pass percentage based dimensions.\n\n```jsx\nimport AutoSizer from \"react-virtualized-auto-sizer\";\n\n// UI\n<AutoSizer>\n  {({ height, width }) => {\n    // Use these actual sizes to calculate your percentage based sizes\n  }}\n</AutoSizer>;\n```\n\n## FAQs\n\n### Can I use this component with flexbox?\n\nFlex containers don't prevent their children from growing and `AutoSizer` greedily grows to fill as much space as possible. Combining the two can be problematic. The simple way to fix this is to nest `AutoSizer` inside of a `block` element (like a `<div>`) rather than putting it as a direct child of the flex container, like so:\n\n```jsx\n<div style={{ display: 'flex' }}>\n  <!-- Other children... -->\n  <div style={{ flex: '1 1 auto' }}>\n    <AutoSizer>\n      {({ height, width }) => (\n        <Component\n          width={width}\n          height={height}\n          {...props}\n        />\n      )}\n    </AutoSizer>\n  </div>\n</div>\n```\n\n### Why is `AutoSizer` passing a height of 0?\n\n`AutoSizer` expands to _fill_ its parent but it will not _stretch_ the parent. This is done to prevent problems with flexbox layouts. If `AutoSizer` is reporting a height (or width) of 0- then it's likely that the parent element (or one of its parents) has a height of 0.\n\nThe solution to this problem is often to add `height: 100%` or `flex: 1` to the parent. One easy way to test this is to add a style property (eg `background-color: red;`) to the parent to visually confirm that it is the expected size.\n\n### Can I use `AutoSizer` to manage only width or height (not both)?\n\nYou can use `AutoSizer` to control only one dimension of its child component using the `disableHeight` or `disableWidth` attributes. For example, a fixed-height component that should grow to fill the available width can be created like so:\n\n```jsx\n<AutoSizer disableHeight>\n  {({width}) => <Component height={200} width={width} {...props} />}\n</AutoSizer>\n```\n\n\n### Module parsing fails because of an unexpected token?\n\nThis package targets [ECMAScript 2015](https://262.ecma-international.org/6.0/) (ES6) and requires a build tool such as [babel-loader](https://www.npmjs.com/package/babel-loader) that is capable of parsing the ES6 `class` syntax.\n\n### Can this component work with a Content Security Policy?\n\n[The specification of Content Security Policy](https://www.w3.org/TR/2016/REC-CSP2-20161215/#intro)\ndescribes as the following:\n\n> This document defines Content Security Policy, a mechanism web applications\n> can use to mitigate a broad class of content injection vulnerabilities, such\n> as cross-site scripting (XSS).\n\nTo apply Content Security Policy, pass a `nonce` to `AutoSizer` and add a matching `nonce-source` to the `Content-Security-Policy` field in HTTP header.\n"}