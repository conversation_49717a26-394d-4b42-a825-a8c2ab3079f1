import{each as he,useIsomorphicLayoutEffect as sn}from"@react-spring/shared";import{is as K,toArray as Wt,eachProp as dt,getFluidValue as $t,isAnimatedString as Zt,Globals as en}from"@react-spring/shared";function I(t,...n){return K.fun(t)?t(...n):t}var ee=(t,n)=>t===!0||!!(n&&t&&(K.fun(t)?t(n):Wt(t).includes(n))),Ze=(t,n)=>K.obj(t)?n&&t[n]:t;var ke=(t,n)=>t.default===!0?t[n]:t.default?t.default[n]:void 0,tn=t=>t,te=(t,n=tn)=>{let e=nn;t.default&&t.default!==!0&&(t=t.default,e=Object.keys(t));let r={};for(let o of e){let s=n(t[o],o);K.und(s)||(r[o]=s)}return r},nn=["config","onProps","onStart","onChange","onPause","onResume","onRest"],rn={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function on(t){let n={},e=0;if(dt(t,(r,o)=>{rn[o]||(n[o]=r,e++)}),e)return n}function fe(t){let n=on(t);if(n){let e={to:n};return dt(t,(r,o)=>o in n||(e[o]=r)),e}return{...t}}function de(t){return t=$t(t),K.arr(t)?t.map(de):Zt(t)?en.createStringInterpolator({range:[0,1],output:[t,t]})(1):t}function Ue(t){for(let n in t)return!0;return!1}function Ee(t){return K.fun(t)||K.arr(t)&&K.obj(t[0])}function Te(t,n){t.ref?.delete(t),n?.delete(t)}function me(t,n){n&&t.ref!==n&&(t.ref?.delete(t),n.add(t),t.ref=n)}function Fr(t,n,e=1e3){sn(()=>{if(n){let r=0;he(t,(o,s)=>{let a=o.current;if(a.length){let i=e*n[s];isNaN(i)?i=r:r=i,he(a,p=>{he(p.queue,u=>{let b=u.delay;u.delay=l=>i+I(b||0,l)})}),o.start()}})}else{let r=Promise.resolve();he(t,o=>{let s=o.current;if(s.length){let a=s.map(i=>{let p=i.queue;return i.queue=[],p});r=r.then(()=>(he(s,(i,p)=>he(a[p]||[],u=>i.queue.push(u))),Promise.all(o.start())))}})}})}import{is as jn}from"@react-spring/shared";import{useContext as Un,useMemo as Be,useRef as pt}from"react";import{is as En,each as Ke,usePrev as Dt,useOnce as wn,useForceUpdate as Ln,useIsomorphicLayoutEffect as Mn}from"@react-spring/shared";import{is as R,raf as Re,each as At,isEqual as Y,toArray as Rt,eachProp as Sn,frameLoop as Pn,flushCalls as Qe,getFluidValue as se,isAnimatedString as Tn,Globals as xn,callFluidObservers as bn,hasFluidValue as ye,addFluidObserver as An,removeFluidObserver as Rn,getFluidObservers as vt}from"@react-spring/shared";import{AnimatedValue as vn,AnimatedString as Ct,getPayload as Cn,getAnimated as ie,setAnimated as In,getAnimatedType as It}from"@react-spring/animated";import{is as ne,easings as an}from"@react-spring/shared";var mt={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}};var et={...mt.default,mass:1,damping:1,easing:an.linear,clamp:!1},we=class{constructor(){this.velocity=0;Object.assign(this,et)}};function gt(t,n,e){e&&(e={...e},ht(e,n),n={...e,...n}),ht(t,n),Object.assign(t,n);for(let a in et)t[a]==null&&(t[a]=et[a]);let{frequency:r,damping:o}=t,{mass:s}=t;return ne.und(r)||(r<.01&&(r=.01),o<0&&(o=0),t.tension=Math.pow(2*Math.PI/r,2)*s,t.friction=4*Math.PI*o*s/r),t}function ht(t,n){if(!ne.und(n.decay))t.duration=void 0;else{let e=!ne.und(n.tension)||!ne.und(n.friction);(e||!ne.und(n.frequency)||!ne.und(n.damping)||!ne.und(n.mass))&&(t.duration=void 0,t.decay=void 0),e&&(t.frequency=void 0)}}var yt=[],Le=class{constructor(){this.changed=!1;this.values=yt;this.toValues=null;this.fromValues=yt;this.config=new we;this.immediate=!1}};import{is as un,raf as St,Globals as pn}from"@react-spring/shared";function Me(t,{key:n,props:e,defaultProps:r,state:o,actions:s}){return new Promise((a,i)=>{let p,u,b=ee(e.cancel??r?.cancel,n);if(b)g();else{un.und(e.pause)||(o.paused=ee(e.pause,n));let h=r?.pause;h!==!0&&(h=o.paused||ee(h,n)),p=I(e.delay||0,n),h?(o.resumeQueue.add(y),s.pause()):(s.resume(),y())}function l(){o.resumeQueue.add(y),o.timeouts.delete(u),u.cancel(),p=u.time-St.now()}function y(){p>0&&!pn.skipAnimation?(o.delayed=!0,u=St.setTimeout(g,p),o.pauseQueue.add(l),o.timeouts.add(u)):g()}function g(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(l),o.timeouts.delete(u),t<=(o.cancelId||0)&&(b=!0);try{s.start({...e,callId:t,cancel:b},a)}catch(h){i(h)}}})}import{is as je,raf as ln,flush as cn,eachProp as fn,Globals as Pt}from"@react-spring/shared";var xe=(t,n)=>n.length==1?n[0]:n.some(e=>e.cancelled)?q(t.get()):n.every(e=>e.noop)?tt(t.get()):w(t.get(),n.every(e=>e.finished)),tt=t=>({value:t,noop:!0,finished:!0,cancelled:!1}),w=(t,n,e=!1)=>({value:t,finished:n,cancelled:e}),q=t=>({value:t,cancelled:!0,finished:!1});function Ne(t,n,e,r){let{callId:o,parentId:s,onRest:a}=n,{asyncTo:i,promise:p}=e;return!s&&t===i&&!n.reset?p:e.promise=(async()=>{e.asyncId=o,e.asyncTo=t;let u=te(n,(d,f)=>f==="onRest"?void 0:d),b,l,y=new Promise((d,f)=>(b=d,l=f)),g=d=>{let f=o<=(e.cancelId||0)&&q(r)||o!==e.asyncId&&w(r,!1);if(f)throw d.result=f,l(d),d},h=(d,f)=>{let T=new be,S=new De;return(async()=>{if(Pt.skipAnimation)throw re(e),S.result=w(r,!1),l(S),S;g(T);let A=je.obj(d)?{...d}:{...f,to:d};A.parentId=o,fn(u,(C,v)=>{je.und(A[v])&&(A[v]=C)});let x=await r.start(A);return g(T),e.paused&&await new Promise(C=>{e.resumeQueue.add(C)}),x})()},c;if(Pt.skipAnimation)return re(e),w(r,!1);try{let d;je.arr(t)?d=(async f=>{for(let T of f)await h(T)})(t):d=Promise.resolve(t(h,r.stop.bind(r))),await Promise.all([d.then(b),y]),c=w(r.get(),!0,!1)}catch(d){if(d instanceof be)c=d.result;else if(d instanceof De)c=d.result;else throw d}finally{o==e.asyncId&&(e.asyncId=s,e.asyncTo=s?i:void 0,e.promise=s?p:void 0)}return je.fun(a)&&ln.batchedUpdates(()=>{a(c,r,r.item)}),c})()}function re(t,n){cn(t.timeouts,e=>e.cancel()),t.pauseQueue.clear(),t.resumeQueue.clear(),t.asyncId=t.asyncTo=t.promise=void 0,n&&(t.cancelId=n)}var be=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},De=class extends Error{constructor(){super("SkipAnimationSignal")}};import{deprecateInterpolate as dn,frameLoop as mn,FluidValue as hn,Globals as Tt,callFluidObservers as xt}from"@react-spring/shared";import{getAnimated as gn}from"@react-spring/animated";var Ae=t=>t instanceof J,yn=1,J=class extends hn{constructor(){super(...arguments);this.id=yn++;this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=gn(this);return e&&e.getValue()}to(...e){return Tt.to(this,e)}interpolate(...e){return dn(),Tt.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,r=!1){xt(this,{type:"change",parent:this,value:e,idle:r})}_onPriorityChange(e){this.idle||mn.sort(this),xt(this,{type:"priority",parent:this,priority:e})}};var oe=Symbol.for("SpringPhase"),bt=1,nt=2,rt=4,qe=t=>(t[oe]&bt)>0,Q=t=>(t[oe]&nt)>0,ge=t=>(t[oe]&rt)>0,ot=(t,n)=>n?t[oe]|=nt|bt:t[oe]&=~nt,st=(t,n)=>n?t[oe]|=rt:t[oe]&=~rt;var ae=class extends J{constructor(e,r){super();this.animation=new Le;this.defaultProps={};this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._pendingCalls=new Set;this._lastCallId=0;this._lastToId=0;this._memoizedDuration=0;if(!R.und(e)||!R.und(r)){let o=R.obj(e)?{...e}:{...r,from:e};R.und(o.default)&&(o.default=!0),this.start(o)}}get idle(){return!(Q(this)||this._state.asyncTo)||ge(this)}get goal(){return se(this.animation.to)}get velocity(){let e=ie(this);return e instanceof vn?e.lastVelocity||0:e.getPayload().map(r=>r.lastVelocity||0)}get hasAnimated(){return qe(this)}get isAnimating(){return Q(this)}get isPaused(){return ge(this)}get isDelayed(){return this._state.delayed}advance(e){let r=!0,o=!1,s=this.animation,{toValues:a}=s,{config:i}=s,p=Cn(s.to);!p&&ye(s.to)&&(a=Rt(se(s.to))),s.values.forEach((l,y)=>{if(l.done)return;let g=l.constructor==Ct?1:p?p[y].lastPosition:a[y],h=s.immediate,c=g;if(!h){if(c=l.lastPosition,i.tension<=0){l.done=!0;return}let d=l.elapsedTime+=e,f=s.fromValues[y],T=l.v0!=null?l.v0:l.v0=R.arr(i.velocity)?i.velocity[y]:i.velocity,S,A=i.precision||(f==g?.005:Math.min(1,Math.abs(g-f)*.001));if(R.und(i.duration))if(i.decay){let x=i.decay===!0?.998:i.decay,C=Math.exp(-(1-x)*d);c=f+T/(1-x)*(1-C),h=Math.abs(l.lastPosition-c)<=A,S=T*C}else{S=l.lastVelocity==null?T:l.lastVelocity;let x=i.restVelocity||A/10,C=i.clamp?0:i.bounce,v=!R.und(C),U=f==g?l.v0>0:f<g,V,M=!1,k=1,X=Math.ceil(e/k);for(let L=0;L<X&&(V=Math.abs(S)>x,!(!V&&(h=Math.abs(g-c)<=A,h)));++L){v&&(M=c==g||c>g==U,M&&(S=-S*C,c=g));let m=-i.tension*1e-6*(c-g),P=-i.friction*.001*S,_=(m+P)/i.mass;S=S+_*k,c=c+S*k}}else{let x=1;i.duration>0&&(this._memoizedDuration!==i.duration&&(this._memoizedDuration=i.duration,l.durationProgress>0&&(l.elapsedTime=i.duration*l.durationProgress,d=l.elapsedTime+=e)),x=(i.progress||0)+d/this._memoizedDuration,x=x>1?1:x<0?0:x,l.durationProgress=x),c=f+i.easing(x)*(g-f),S=(c-l.lastPosition)/e,h=x==1}l.lastVelocity=S,Number.isNaN(c)&&(console.warn("Got NaN while animating:",this),h=!0)}p&&!p[y].done&&(h=!1),h?l.done=!0:r=!1,l.setValue(c,i.round)&&(o=!0)});let u=ie(this),b=u.getValue();if(r){let l=se(s.to);(b!==l||o)&&!i.decay?(u.setValue(l),this._onChange(l)):o&&i.decay&&this._onChange(b),this._stop()}else o&&this._onChange(b)}set(e){return Re.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(Q(this)){let{to:e,config:r}=this.animation;Re.batchedUpdates(()=>{this._onStart(),r.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,r){let o;return R.und(e)?(o=this.queue||[],this.queue=[]):o=[R.obj(e)?e:{...r,to:e}],Promise.all(o.map(s=>this._update(s))).then(s=>xe(this,s))}stop(e){let{to:r}=this.animation;return this._focus(this.get()),re(this._state,e&&this._lastCallId),Re.batchedUpdates(()=>this._stop(r,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){let r=this.key||"",{to:o,from:s}=e;o=R.obj(o)?o[r]:o,(o==null||Ee(o))&&(o=void 0),s=R.obj(s)?s[r]:s,s==null&&(s=void 0);let a={to:o,from:s};return qe(this)||(e.reverse&&([o,s]=[s,o]),s=se(s),R.und(s)?ie(this)||this._set(o):this._set(s)),a}_update({...e},r){let{key:o,defaultProps:s}=this;e.default&&Object.assign(s,te(e,(p,u)=>/^on/.test(u)?Ze(p,o):p)),_t(this,e,"onProps"),Ce(this,"onProps",e,this);let a=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let i=this._state;return Me(++this._lastCallId,{key:o,props:e,defaultProps:s,state:i,actions:{pause:()=>{ge(this)||(st(this,!0),Qe(i.pauseQueue),Ce(this,"onPause",w(this,ve(this,this.animation.to)),this))},resume:()=>{ge(this)&&(st(this,!1),Q(this)&&this._resume(),Qe(i.resumeQueue),Ce(this,"onResume",w(this,ve(this,this.animation.to)),this))},start:this._merge.bind(this,a)}}).then(p=>{if(e.loop&&p.finished&&!(r&&p.noop)){let u=it(e);if(u)return this._update(u,!0)}return p})}_merge(e,r,o){if(r.cancel)return this.stop(!0),o(q(this));let s=!R.und(e.to),a=!R.und(e.from);if(s||a)if(r.callId>this._lastToId)this._lastToId=r.callId;else return o(q(this));let{key:i,defaultProps:p,animation:u}=this,{to:b,from:l}=u,{to:y=b,from:g=l}=e;a&&!s&&(!r.default||R.und(y))&&(y=g),r.reverse&&([y,g]=[g,y]);let h=!Y(g,l);h&&(u.from=g),g=se(g);let c=!Y(y,b);c&&this._focus(y);let d=Ee(r.to),{config:f}=u,{decay:T,velocity:S}=f;(s||a)&&(f.velocity=0),r.config&&!d&&gt(f,I(r.config,i),r.config!==p.config?I(p.config,i):void 0);let A=ie(this);if(!A||R.und(y))return o(w(this,!0));let x=R.und(r.reset)?a&&!r.default:!R.und(g)&&ee(r.reset,i),C=x?g:this.get(),v=de(y),U=R.num(v)||R.arr(v)||Tn(v),V=!d&&(!U||ee(p.immediate||r.immediate,i));if(c){let L=It(y);if(L!==A.constructor)if(V)A=this._set(v);else throw Error(`Cannot animate between ${A.constructor.name} and ${L.name}, as the "to" prop suggests`)}let M=A.constructor,k=ye(y),X=!1;if(!k){let L=x||!qe(this)&&h;(c||L)&&(X=Y(de(C),v),k=!X),(!Y(u.immediate,V)&&!V||!Y(f.decay,T)||!Y(f.velocity,S))&&(k=!0)}if(X&&Q(this)&&(u.changed&&!x?k=!0:k||this._stop(b)),!d&&((k||ye(b))&&(u.values=A.getPayload(),u.toValues=ye(y)?null:M==Ct?[1]:Rt(v)),u.immediate!=V&&(u.immediate=V,!V&&!x&&this._set(b)),k)){let{onRest:L}=u;At(Vn,P=>_t(this,r,P));let m=w(this,ve(this,b));Qe(this._pendingCalls,m),this._pendingCalls.add(o),u.changed&&Re.batchedUpdates(()=>{u.changed=!x,L?.(m,this),x?I(p.onRest,m):u.onStart?.(m,this)})}x&&this._set(C),d?o(Ne(r.to,r,this._state,this)):k?this._start():Q(this)&&!c?this._pendingCalls.add(o):o(tt(C))}_focus(e){let r=this.animation;e!==r.to&&(vt(this)&&this._detach(),r.to=e,vt(this)&&this._attach())}_attach(){let e=0,{to:r}=this.animation;ye(r)&&(An(r,this),Ae(r)&&(e=r.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;ye(e)&&Rn(e,this)}_set(e,r=!0){let o=se(e);if(!R.und(o)){let s=ie(this);if(!s||!Y(o,s.getValue())){let a=It(o);!s||s.constructor!=a?In(this,a.create(o)):s.setValue(o),s&&Re.batchedUpdates(()=>{this._onChange(o,r)})}}return ie(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,Ce(this,"onStart",w(this,ve(this,e.to)),this))}_onChange(e,r){r||(this._onStart(),I(this.animation.onChange,e,this)),I(this.defaultProps.onChange,e,this),super._onChange(e,r)}_start(){let e=this.animation;ie(this).reset(se(e.to)),e.immediate||(e.fromValues=e.values.map(r=>r.lastPosition)),Q(this)||(ot(this,!0),ge(this)||this._resume())}_resume(){xn.skipAnimation?this.finish():Pn.start(this)}_stop(e,r){if(Q(this)){ot(this,!1);let o=this.animation;At(o.values,a=>{a.done=!0}),o.toValues&&(o.onChange=o.onPause=o.onResume=void 0),bn(this,{type:"idle",parent:this});let s=r?q(this.get()):w(this.get(),ve(this,e??o.to));Qe(this._pendingCalls,s),o.changed&&(o.changed=!1,Ce(this,"onRest",s,this))}}};function ve(t,n){let e=de(n),r=de(t.get());return Y(r,e)}function it(t,n=t.loop,e=t.to){let r=I(n);if(r){let o=r!==!0&&fe(r),s=(o||t).reverse,a=!o||o.reset;return Se({...t,loop:n,default:!1,pause:void 0,to:!s||Ee(e)?e:void 0,from:a?t.from:void 0,reset:a,...o})}}function Se(t){let{to:n,from:e}=t=fe(t),r=new Set;return R.obj(n)&&Vt(n,r),R.obj(e)&&Vt(e,r),t.keys=r.size?Array.from(r):null,t}function Ot(t){let n=Se(t);return R.und(n.default)&&(n.default=te(n)),n}function Vt(t,n){Sn(t,(e,r)=>e!=null&&n.add(r))}var Vn=["onStart","onRest","onChange","onPause","onResume"];function _t(t,n,e){t.animation[e]=n[e]!==ke(n,e)?Ze(n[e],t.key):void 0}function Ce(t,n,...e){t.animation[n]?.(...e),t.defaultProps[n]?.(...e)}import{is as z,raf as kt,each as ue,noop as Ft,flush as at,toArray as Ie,eachProp as Ut,flushCalls as _n,addFluidObserver as Et}from"@react-spring/shared";var On=["onStart","onChange","onRest"],Fn=1,pe=class{constructor(n,e){this.id=Fn++;this.springs={};this.queue=[];this._lastAsyncId=0;this._active=new Set;this._changed=new Set;this._started=!1;this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._events={onStart:new Map,onChange:new Map,onRest:new Map};this._onFrame=this._onFrame.bind(this),e&&(this._flush=e),n&&this.start({default:!0,...n})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(n=>n.idle&&!n.isDelayed&&!n.isPaused)}get item(){return this._item}set item(n){this._item=n}get(){let n={};return this.each((e,r)=>n[r]=e.get()),n}set(n){for(let e in n){let r=n[e];z.und(r)||this.springs[e].set(r)}}update(n){return n&&this.queue.push(Se(n)),this}start(n){let{queue:e}=this;return n?e=Ie(n).map(Se):this.queue=[],this._flush?this._flush(this,e):(jt(this,e),ze(this,e))}stop(n,e){if(n!==!!n&&(e=n),e){let r=this.springs;ue(Ie(e),o=>r[o].stop(!!n))}else re(this._state,this._lastAsyncId),this.each(r=>r.stop(!!n));return this}pause(n){if(z.und(n))this.start({pause:!0});else{let e=this.springs;ue(Ie(n),r=>e[r].pause())}return this}resume(n){if(z.und(n))this.start({pause:!1});else{let e=this.springs;ue(Ie(n),r=>e[r].resume())}return this}each(n){Ut(this.springs,n)}_onFrame(){let{onStart:n,onChange:e,onRest:r}=this._events,o=this._active.size>0,s=this._changed.size>0;(o&&!this._started||s&&!this._started)&&(this._started=!0,at(n,([p,u])=>{u.value=this.get(),p(u,this,this._item)}));let a=!o&&this._started,i=s||a&&r.size?this.get():null;s&&e.size&&at(e,([p,u])=>{u.value=i,p(u,this,this._item)}),a&&(this._started=!1,at(r,([p,u])=>{u.value=i,p(u,this,this._item)}))}eventObserved(n){if(n.type=="change")this._changed.add(n.parent),n.idle||this._active.add(n.parent);else if(n.type=="idle")this._active.delete(n.parent);else return;kt.onFrame(this._onFrame)}};function ze(t,n){return Promise.all(n.map(e=>wt(t,e))).then(e=>xe(t,e))}async function wt(t,n,e){let{keys:r,to:o,from:s,loop:a,onRest:i,onResolve:p}=n,u=z.obj(n.default)&&n.default;a&&(n.loop=!1),o===!1&&(n.to=null),s===!1&&(n.from=null);let b=z.arr(o)||z.fun(o)?o:void 0;b?(n.to=void 0,n.onRest=void 0,u&&(u.onRest=void 0)):ue(On,c=>{let d=n[c];if(z.fun(d)){let f=t._events[c];n[c]=({finished:T,cancelled:S})=>{let A=f.get(d);A?(T||(A.finished=!1),S&&(A.cancelled=!0)):f.set(d,{value:null,finished:T||!1,cancelled:S||!1})},u&&(u[c]=n[c])}});let l=t._state;n.pause===!l.paused?(l.paused=n.pause,_n(n.pause?l.pauseQueue:l.resumeQueue)):l.paused&&(n.pause=!0);let y=(r||Object.keys(t.springs)).map(c=>t.springs[c].start(n)),g=n.cancel===!0||ke(n,"cancel")===!0;(b||g&&l.asyncId)&&y.push(Me(++t._lastAsyncId,{props:n,state:l,actions:{pause:Ft,resume:Ft,start(c,d){g?(re(l,t._lastAsyncId),d(q(t))):(c.onRest=i,d(Ne(b,c,l,t)))}}})),l.paused&&await new Promise(c=>{l.resumeQueue.add(c)});let h=xe(t,await Promise.all(y));if(a&&h.finished&&!(e&&h.noop)){let c=it(n,a,o);if(c)return jt(t,[c]),wt(t,c,!0)}return p&&kt.batchedUpdates(()=>p(h,t,t.item)),h}function Ve(t,n){let e={...t.springs};return n&&ue(Ie(n),r=>{z.und(r.keys)&&(r=Se(r)),z.obj(r.to)||(r={...r,to:void 0}),Mt(e,r,o=>Lt(o))}),ut(t,e),e}function ut(t,n){Ut(n,(e,r)=>{t.springs[r]||(t.springs[r]=e,Et(e,t))})}function Lt(t,n){let e=new ae;return e.key=t,n&&Et(e,n),e}function Mt(t,n,e){n.keys&&ue(n.keys,r=>{(t[r]||(t[r]=e(r)))._prepareNode(n)})}function jt(t,n){ue(n,e=>{Mt(t.springs,e,r=>Lt(r,t))})}import*as Ge from"react";import{useContext as vo}from"react";var _e=Ge.createContext({pause:!1,immediate:!1});import{each as le,is as Xe,deprecateDirectCall as kn}from"@react-spring/shared";var ce=()=>{let t=[],n=function(r){kn();let o=[];return le(t,(s,a)=>{if(Xe.und(r))o.push(s.start());else{let i=e(r,s,a);i&&o.push(s.start(i))}}),o};n.current=t,n.add=function(r){t.includes(r)||t.push(r)},n.delete=function(r){let o=t.indexOf(r);~o&&t.splice(o,1)},n.pause=function(){return le(t,r=>r.pause(...arguments)),this},n.resume=function(){return le(t,r=>r.resume(...arguments)),this},n.set=function(r){le(t,(o,s)=>{let a=Xe.fun(r)?r(s,o):r;a&&o.set(a)})},n.start=function(r){let o=[];return le(t,(s,a)=>{if(Xe.und(r))o.push(s.start());else{let i=this._getProps(r,s,a);i&&o.push(s.start(i))}}),o},n.stop=function(){return le(t,r=>r.stop(...arguments)),this},n.update=function(r){return le(t,(o,s)=>o.update(this._getProps(r,o,s))),this};let e=function(r,o,s){return Xe.fun(r)?r(s,o):r};return n._getProps=e,n};function Je(t,n,e){let r=En.fun(n)&&n;r&&!e&&(e=[]);let o=Be(()=>r||arguments.length==3?ce():void 0,[]),s=pt(0),a=Ln(),i=Be(()=>({ctrls:[],queue:[],flush(f,T){let S=Ve(f,T);return s.current>0&&!i.queue.length&&!Object.keys(S).some(x=>!f.springs[x])?ze(f,T):new Promise(x=>{ut(f,S),i.queue.push(()=>{x(ze(f,T))}),a()})}}),[]),p=pt([...i.ctrls]),u=pt([]),b=Dt(t)||0;Be(()=>{Ke(p.current.slice(t,b),f=>{Te(f,o),f.stop(!0)}),p.current.length=t,l(b,t)},[t]),Be(()=>{l(0,Math.min(b,t))},e);function l(f,T){for(let S=f;S<T;S++){let A=p.current[S]||(p.current[S]=new pe(null,i.flush)),x=r?r(S,A):n[S];x&&(u.current[S]=Ot(x))}}let y=p.current.map((f,T)=>Ve(f,u.current[T])),g=Un(_e),h=Dt(g),c=g!==h&&Ue(g);Mn(()=>{s.current++,i.ctrls=p.current;let{queue:f}=i;f.length&&(i.queue=[],Ke(f,T=>T())),Ke(p.current,(T,S)=>{o?.add(T),c&&T.start({default:g});let A=u.current[S];A&&(me(T,A.ref),T.ref?T.queue.push(A):T.start(A))})}),wn(()=>()=>{Ke(i.ctrls,f=>f.stop(!0))});let d=y.map(f=>({...f}));return o?[d,o]:d}function H(t,n){let e=jn.fun(t),[[r],o]=Je(1,e?t:[t],e?n||[]:n);return e||arguments.length==2?[r,o]:r}import{useState as Dn}from"react";var Nn=()=>ce(),Go=()=>Dn(Nn)[0];import{useConstant as qn,useOnce as Qn}from"@react-spring/shared";var Jo=(t,n)=>{let e=qn(()=>new ae(t,n));return Qn(()=>()=>{e.stop()}),e};import{each as zn,is as Nt,useIsomorphicLayoutEffect as Gn}from"@react-spring/shared";function qt(t,n,e){let r=Nt.fun(n)&&n;r&&!e&&(e=[]);let o=!0,s,a=Je(t,(i,p)=>{let u=r?r(i,p):n;return s=u.ref,o=o&&u.reverse,u},e||[{}]);if(Gn(()=>{zn(a[1].current,(i,p)=>{let u=a[1].current[p+(o?1:-1)];if(me(i,s),i.ref){u&&i.update({to:u.springs});return}u?i.start({to:u.springs}):i.start()})},e),r||arguments.length==3){let i=s??a[1];return i._getProps=(p,u,b)=>{let l=Nt.fun(p)?p(b,u):p;if(l){let y=i.current[b+(l.reverse?1:-1)];return y&&(l.to=y.springs),l}},a}return a[0]}import*as Oe from"react";import{useContext as Xn,useRef as lt,useMemo as Bn}from"react";import{is as G,toArray as Qt,useForceUpdate as Kn,useOnce as Jn,usePrev as Yn,each as D,useIsomorphicLayoutEffect as Ye}from"@react-spring/shared";function zt(t,n,e){let r=G.fun(n)&&n,{reset:o,sort:s,trail:a=0,expires:i=!0,exitBeforeEnter:p=!1,onDestroyed:u,ref:b,config:l}=r?r():n,y=Bn(()=>r||arguments.length==3?ce():void 0,[]),g=Qt(t),h=[],c=lt(null),d=o?null:c.current;Ye(()=>{c.current=h}),Jn(()=>(D(h,m=>{y?.add(m.ctrl),m.ctrl.ref=y}),()=>{D(c.current,m=>{m.expired&&clearTimeout(m.expirationId),Te(m.ctrl,y),m.ctrl.stop(!0)})}));let f=Wn(g,r?r():n,d),T=o&&c.current||[];Ye(()=>D(T,({ctrl:m,item:P,key:_})=>{Te(m,y),I(u,P,_)}));let S=[];if(d&&D(d,(m,P)=>{m.expired?(clearTimeout(m.expirationId),T.push(m)):(P=S[P]=f.indexOf(m.key),~P&&(h[P]=m))}),D(g,(m,P)=>{h[P]||(h[P]={key:f[P],item:m,phase:"mount",ctrl:new pe},h[P].ctrl.item=m)}),S.length){let m=-1,{leave:P}=r?r():n;D(S,(_,F)=>{let O=d[F];~_?(m=h.indexOf(O),h[m]={...O,item:g[_]}):P&&h.splice(++m,0,O)})}G.fun(s)&&h.sort((m,P)=>s(m.item,P.item));let A=-a,x=Kn(),C=te(n),v=new Map,U=lt(new Map),V=lt(!1);D(h,(m,P)=>{let _=m.key,F=m.phase,O=r?r():n,E,N,Yt=I(O.delay||0,_);if(F=="mount")E=O.enter,N="enter";else{let j=f.indexOf(_)<0;if(F!="leave")if(j)E=O.leave,N="leave";else if(E=O.update)N="update";else return;else if(!j)E=O.enter,N="enter";else return}if(E=I(E,m.item,P),E=G.obj(E)?fe(E):{to:E},!E.config){let j=l||C.config;E.config=I(j,m.item,P,N)}A+=a;let $={...C,delay:Yt+A,ref:b,immediate:O.immediate,reset:!1,...E};if(N=="enter"&&G.und($.from)){let j=r?r():n,Pe=G.und(j.initial)||d?j.from:j.initial;$.from=I(Pe,m.item,P)}let{onResolve:Ht}=$;$.onResolve=j=>{I(Ht,j);let Pe=c.current,B=Pe.find(Fe=>Fe.key===_);if(B&&!(j.cancelled&&B.phase!="update")&&B.ctrl.idle){let Fe=Pe.every(Z=>Z.ctrl.idle);if(B.phase=="leave"){let Z=I(i,B.item);if(Z!==!1){let $e=Z===!0?0:Z;if(B.expired=!0,!Fe&&$e>0){$e<=2147483647&&(B.expirationId=setTimeout(x,$e));return}}}Fe&&Pe.some(Z=>Z.expired)&&(U.current.delete(B),p&&(V.current=!0),x())}};let ft=Ve(m.ctrl,$);N==="leave"&&p?U.current.set(m,{phase:N,springs:ft,payload:$}):v.set(m,{phase:N,springs:ft,payload:$})});let M=Xn(_e),k=Yn(M),X=M!==k&&Ue(M);Ye(()=>{X&&D(h,m=>{m.ctrl.start({default:M})})},[M]),D(v,(m,P)=>{if(U.current.size){let _=h.findIndex(F=>F.key===P.key);h.splice(_,1)}}),Ye(()=>{D(U.current.size?U.current:v,({phase:m,payload:P},_)=>{let{ctrl:F}=_;_.phase=m,y?.add(F),X&&m=="enter"&&F.start({default:M}),P&&(me(F,P.ref),(F.ref||y)&&!V.current?F.update(P):(F.start(P),V.current&&(V.current=!1)))})},o?void 0:e);let L=m=>Oe.createElement(Oe.Fragment,null,h.map((P,_)=>{let{springs:F}=v.get(P)||P.ctrl,O=m({...F},P.item,P,_);return O&&O.type?Oe.createElement(O.type,{...O.props,key:G.str(P.key)||G.num(P.key)?P.key:P.ctrl.id,ref:O.ref}):O}));return y?[L,y]:L}var Hn=1;function Wn(t,{key:n,keys:e=n},r){if(e===null){let o=new Set;return t.map(s=>{let a=r&&r.find(i=>i.item===s&&i.phase!=="leave"&&!o.has(i));return a?(o.add(a),a.key):Hn++})}return G.und(e)?t:G.fun(e)?t.map(e):Qt(e)}import{each as $n,onScroll as Zn,useIsomorphicLayoutEffect as er}from"@react-spring/shared";var fs=({container:t,...n}={})=>{let[e,r]=H(()=>({scrollX:0,scrollY:0,scrollXProgress:0,scrollYProgress:0,...n}),[]);return er(()=>{let o=Zn(({x:s,y:a})=>{r.start({scrollX:s.current,scrollXProgress:s.progress,scrollY:a.current,scrollYProgress:a.progress})},{container:t?.current||void 0});return()=>{$n(Object.values(e),s=>s.stop()),o()}},[]),e};import{onResize as tr,each as nr,useIsomorphicLayoutEffect as rr}from"@react-spring/shared";var gs=({container:t,...n})=>{let[e,r]=H(()=>({width:0,height:0,...n}),[]);return rr(()=>{let o=tr(({width:s,height:a})=>{r.start({width:s,height:a,immediate:e.width.get()===0||e.height.get()===0})},{container:t?.current||void 0});return()=>{nr(Object.values(e),s=>s.stop()),o()}},[]),e};import{useRef as or,useState as sr}from"react";import{is as Gt,useIsomorphicLayoutEffect as ir}from"@react-spring/shared";var ar={any:0,all:1};function As(t,n){let[e,r]=sr(!1),o=or(void 0),s=Gt.fun(t)&&t,a=s?s():{},{to:i={},from:p={},...u}=a,b=s?n:t,[l,y]=H(()=>({from:p,...u}),[]);return ir(()=>{let g=o.current,{root:h,once:c,amount:d="any",...f}=b??{};if(!g||c&&e||typeof IntersectionObserver>"u")return;let T=new WeakMap,S=()=>(i&&y.start(i),r(!0),c?void 0:()=>{p&&y.start(p),r(!1)}),A=C=>{C.forEach(v=>{let U=T.get(v.target);if(v.isIntersecting!==!!U)if(v.isIntersecting){let V=S();Gt.fun(V)?T.set(v.target,V):x.unobserve(v.target)}else U&&(U(),T.delete(v.target))})},x=new IntersectionObserver(A,{root:h&&h.current||void 0,threshold:typeof d=="number"||Array.isArray(d)?d:ar[d],...f});return x.observe(g),()=>x.unobserve(g)},[b]),s?[o,l]:[o,e]}function js({children:t,...n}){return t(H(n))}import{is as ur}from"@react-spring/shared";function Qs({items:t,children:n,...e}){let r=qt(t.length,e);return t.map((o,s)=>{let a=n(o,s);return ur.fun(a)?a(r[s]):a})}function Xs({items:t,children:n,...e}){return zt(t,e)(n)}import{deprecateInterpolate as br}from"@react-spring/shared";import{is as pr,raf as lr,each as We,isEqual as cr,toArray as He,frameLoop as fr,getFluidValue as Xt,createInterpolator as dr,Globals as mr,callFluidObservers as hr,addFluidObserver as gr,removeFluidObserver as yr,hasFluidValue as Bt}from"@react-spring/shared";import{getAnimated as Sr,setAnimated as Pr,getAnimatedType as Tr,getPayload as Jt}from"@react-spring/animated";var W=class extends J{constructor(e,r){super();this.source=e;this.idle=!0;this._active=new Set;this.calc=dr(...r);let o=this._get(),s=Tr(o);Pr(this,s.create(o))}advance(e){let r=this._get(),o=this.get();cr(r,o)||(Sr(this).setValue(r),this._onChange(r,this.idle)),!this.idle&&Kt(this._active)&&ct(this)}_get(){let e=pr.arr(this.source)?this.source.map(Xt):He(Xt(this.source));return this.calc(...e)}_start(){this.idle&&!Kt(this._active)&&(this.idle=!1,We(Jt(this),e=>{e.done=!1}),mr.skipAnimation?(lr.batchedUpdates(()=>this.advance()),ct(this)):fr.start(this))}_attach(){let e=1;We(He(this.source),r=>{Bt(r)&&gr(r,this),Ae(r)&&(r.idle||this._active.add(r),e=Math.max(e,r.priority+1))}),this.priority=e,this._start()}_detach(){We(He(this.source),e=>{Bt(e)&&yr(e,this)}),this._active.clear(),ct(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=He(this.source).reduce((r,o)=>Math.max(r,(Ae(o)?o.priority:0)+1),0))}};function xr(t){return t.idle!==!1}function Kt(t){return!t.size||Array.from(t).every(xr)}function ct(t){t.idle||(t.idle=!0,We(Jt(t),n=>{n.done=!0}),hr(t,{type:"idle",parent:t}))}var si=(t,...n)=>new W(t,n),ii=(t,...n)=>(br(),new W(t,n));import{Globals as Ar,frameLoop as Rr,createStringInterpolator as vr}from"@react-spring/shared";Ar.assign({createStringInterpolator:vr,to:(t,n)=>new W(t,n)});var li=Rr.advance;import{createInterpolator as Oi,useIsomorphicLayoutEffect as Fi,useReducedMotion as ki,easings as Ui}from"@react-spring/shared";export*from"@react-spring/types";export{be as BailSignal,pe as Controller,J as FrameValue,Ar as Globals,W as Interpolation,js as Spring,_e as SpringContext,ce as SpringRef,ae as SpringValue,Qs as Trail,Xs as Transition,mt as config,Oi as createInterpolator,Ui as easings,fe as inferTo,ii as interpolate,si as to,li as update,Fr as useChain,As as useInView,Fi as useIsomorphicLayoutEffect,ki as useReducedMotion,gs as useResize,fs as useScroll,H as useSpring,Go as useSpringRef,Jo as useSpringValue,Je as useSprings,qt as useTrail,zt as useTransition};
