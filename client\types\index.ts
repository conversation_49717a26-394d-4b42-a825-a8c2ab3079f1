// Core TypeScript interfaces for the boarding house promotional website

export interface Property {
  id: string
  name: string
  description: string
  type: 'putra' | 'putri' | 'campur'
  price: {
    monthly: number
    deposit: number
    utilities?: number
  }
  location: {
    address: string
    city: string
    district: string
    coordinates: {
      lat: number
      lng: number
    }
  }
  images: PropertyImage[]
  amenities: Amenity[]
  facilities: Facility[]
  rooms: Room[]
  owner: Owner
  contact: ContactInfo
  availability: {
    available: boolean
    availableRooms: number
    totalRooms: number
  }
  ratings: {
    overall: number
    cleanliness: number
    location: number
    facilities: number
    value: number
    totalReviews: number
  }
  features: string[]
  rules: string[]
  nearbyPlaces: NearbyPlace[]
  createdAt: string
  updatedAt: string
  verified: boolean
  featured: boolean
}

export interface PropertyImage {
  id: string
  url: string
  alt: string
  type: 'exterior' | 'interior' | 'room' | 'bathroom' | 'kitchen' | 'common_area'
  isPrimary: boolean
  order: number
}

export interface Amenity {
  id: string
  name: string
  icon: string
  category: 'basic' | 'comfort' | 'security' | 'connectivity' | 'recreation'
  available: boolean
}

export interface Facility {
  id: string
  name: string
  description?: string
  icon: string
  category: 'room' | 'shared' | 'building' | 'outdoor'
  available: boolean
  additionalCost?: number
}

export interface Room {
  id: string
  type: 'single' | 'shared' | 'suite'
  size: number // in square meters
  capacity: number
  price: number
  available: boolean
  amenities: string[]
  images: string[]
  description?: string
}

export interface Owner {
  id: string
  name: string
  avatar?: string
  phone: string
  email?: string
  verified: boolean
  responseTime: string
  joinedDate: string
}

export interface ContactInfo {
  phone: string
  whatsapp?: string
  email?: string
  website?: string
  socialMedia?: {
    instagram?: string
    facebook?: string
    twitter?: string
  }
}

export interface NearbyPlace {
  id: string
  name: string
  type: 'university' | 'mall' | 'hospital' | 'transport' | 'restaurant' | 'bank'
  distance: number // in meters
  walkingTime: number // in minutes
  icon: string
}

export interface User {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  preferences: UserPreferences
  savedProperties: string[]
  inquiries: Inquiry[]
  createdAt: string
}

export interface UserPreferences {
  priceRange: [number, number]
  preferredType: Property['type'] | 'any'
  preferredLocations: string[]
  requiredAmenities: string[]
  maxDistance?: number
}

export interface Inquiry {
  id: string
  propertyId: string
  userId: string
  message: string
  contactMethod: 'phone' | 'whatsapp' | 'email'
  status: 'pending' | 'responded' | 'closed'
  createdAt: string
  respondedAt?: string
}

export interface SearchFilters {
  location?: string
  type: 'semua' | 'putra' | 'putri' | 'campur'
  priceRange: [number, number]
  facilities: string[]
  amenities?: string[]
  sortBy: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'distance' | 'newest'
  radius?: number
  availableOnly?: boolean
  verifiedOnly?: boolean
}

export interface SearchResult {
  properties: Property[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  filters: SearchFilters
}

// Component Props Interfaces
export interface PropertyCardProps {
  property: Property
  variant?: 'default' | 'compact' | 'featured'
  showComparison?: boolean
  onCompare?: (property: Property) => void
  onFavorite?: (propertyId: string) => void
  onInquire?: (property: Property) => void
  className?: string
}

export interface SearchBarProps {
  onSearch: (query: string, filters: SearchFilters) => void
  placeholder?: string
  className?: string
  showFilters?: boolean
  initialFilters?: Partial<SearchFilters>
}

export interface FilterPanelProps {
  filters: SearchFilters
  onFiltersChange: (filters: SearchFilters) => void
  onReset: () => void
  className?: string
}

// Animation and UI Types
export interface AnimationConfig {
  duration: number
  delay?: number
  easing?: string
  stagger?: number
}

export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  neutral: string
  success: string
  warning: string
  error: string
  info: string
}

// 3D Scene Types
export interface Scene3DProps {
  propertyId: string
  roomType?: string
  interactive?: boolean
  autoRotate?: boolean
  showControls?: boolean
  fallbackImage?: string
  className?: string
}

export interface CameraPosition {
  x: number
  y: number
  z: number
}

export interface LightingConfig {
  ambient: number
  directional: {
    intensity: number
    position: [number, number, number]
  }
}

// Form Types
export interface ContactFormData {
  name: string
  email: string
  phone: string
  message: string
  propertyId?: string
  preferredContact: 'phone' | 'whatsapp' | 'email'
  visitDate?: string
}

export interface InquiryFormData extends ContactFormData {
  budget: number
  moveInDate: string
  duration: string
  additionalRequests?: string
}

// Error and Loading States
export interface LoadingState {
  isLoading: boolean
  message?: string
}

export interface ErrorState {
  hasError: boolean
  message: string
  code?: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    hasMore: boolean
  }
}

// Utility Types
export type PropertyType = Property['type']
export type AmenityCategory = Amenity['category']
export type FacilityCategory = Facility['category']
export type SortOption = SearchFilters['sortBy']
export type ContactMethod = ContactFormData['preferredContact']
