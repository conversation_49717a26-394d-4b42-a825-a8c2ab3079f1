import{Globals as G}from"@react-spring/core";import{unstable_batchedUpdates as M}from"react-dom";import{createStringInterpolator as U,colors as D}from"@react-spring/shared";import{createHost as H}from"@react-spring/animated";var k=/^--/;function I(t,e){return e==null||typeof e=="boolean"||e===""?"":typeof e=="number"&&e!==0&&!k.test(t)&&!(f.hasOwnProperty(t)&&f[t])?e+"px":(""+e).trim()}var v={};function V(t,e){if(!t.nodeType||!t.setAttribute)return!1;let r=t.nodeName==="filter"||t.parentNode&&t.parentNode.nodeName==="filter",{className:n,style:o,children:u,scrollTop:l,scrollLeft:a,viewBox:i,...d}=e,m=Object.values(d),c=Object.keys(d).map(s=>r||t.hasAttribute(s)?s:v[s]||(v[s]=s.replace(/([A-Z])/g,p=>"-"+p.toLowerCase())));u!==void 0&&(t.textContent=u);for(let s in o)if(o.hasOwnProperty(s)){let p=I(s,o[s]);k.test(s)?t.style.setProperty(s,p):t.style[s]=p}c.forEach((s,p)=>{t.setAttribute(s,m[p])}),n!==void 0&&(t.className=n),l!==void 0&&(t.scrollTop=l),a!==void 0&&(t.scrollLeft=a),i!==void 0&&t.setAttribute("viewBox",i)}var f={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},F=(t,e)=>t+e.charAt(0).toUpperCase()+e.substring(1),S=["Webkit","Ms","Moz","O"];f=Object.keys(f).reduce((t,e)=>(S.forEach(r=>t[F(r,e)]=t[e]),t),f);import{AnimatedObject as L}from"@react-spring/animated";import{is as h,each as b,toArray as E,eachProp as P,FluidValue as T,getFluidValue as w,callFluidObservers as j,hasFluidValue as A,addFluidObserver as N,removeFluidObserver as R}from"@react-spring/shared";var W=/^(matrix|translate|scale|rotate|skew)/,_=/^(translate)/,$=/^(rotate|skew)/,x=(t,e)=>h.num(t)&&t!==0?t+e:t,g=(t,e)=>h.arr(t)?t.every(r=>g(r,e)):h.num(t)?t===e:parseFloat(t)===e,y=class extends L{constructor({x:e,y:r,z:n,...o}){let u=[],l=[];(e||r||n)&&(u.push([e||0,r||0,n||0]),l.push(a=>[`translate3d(${a.map(i=>x(i,"px")).join(",")})`,g(a,0)])),P(o,(a,i)=>{if(i==="transform")u.push([a||""]),l.push(d=>[d,d===""]);else if(W.test(i)){if(delete o[i],h.und(a))return;let d=_.test(i)?"px":$.test(i)?"deg":"";u.push(E(a)),l.push(i==="rotate3d"?([m,c,s,p])=>[`rotate3d(${m},${c},${s},${x(p,d)})`,g(p,0)]:m=>[`${i}(${m.map(c=>x(c,d)).join(",")})`,g(m,i.startsWith("scale")?1:0)])}}),u.length&&(o.transform=new O(u,l)),super(o)}},O=class extends T{constructor(r,n){super();this.inputs=r;this.transforms=n;this._value=null}get(){return this._value||(this._value=this._get())}_get(){let r="",n=!0;return b(this.inputs,(o,u)=>{let l=w(o[0]),[a,i]=this.transforms[u](h.arr(l)?l:o.map(w));r+=" "+a,n=n&&i}),n?"none":r}observerAdded(r){r==1&&b(this.inputs,n=>b(n,o=>A(o)&&N(o,this)))}observerRemoved(r){r==0&&b(this.inputs,n=>b(n,o=>A(o)&&R(o,this)))}eventObserved(r){r.type=="change"&&(this._value=null),j(this,r)}};var C=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"];export*from"@react-spring/core";G.assign({batchedUpdates:M,createStringInterpolator:U,colors:D});var q=H(C,{applyAnimatedValues:V,createAnimatedStyle:t=>new y(t),getComponentProps:({scrollTop:t,scrollLeft:e,...r})=>r}),it=q.animated;export{it as a,it as animated};
