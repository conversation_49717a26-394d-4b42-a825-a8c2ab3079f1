// Custom React hooks for the boarding house promotional website

import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { useInView } from 'react-intersection-observer'
import { Property, SearchFilters, SearchResult, LoadingState, ErrorState } from '@/types'

// Search and filtering hooks
export function useSearch(initialFilters?: Partial<SearchFilters>) {
  const [filters, setFilters] = useState<SearchFilters>({
    type: 'semua',
    priceRange: [500000, 5000000],
    facilities: [],
    sortBy: 'relevance',
    ...initialFilters
  })
  
  const [results, setResults] = useState<SearchResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (query: string, newFilters?: Partial<SearchFilters>) => {
    setLoading(true)
    setError(null)
    
    const searchFilters = { ...filters, ...newFilters }
    setFilters(searchFilters)

    try {
      // Simulate API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock search results
      const mockResults: SearchResult = {
        properties: [], // Would be populated by API
        total: 0,
        page: 1,
        limit: 20,
        hasMore: false,
        filters: searchFilters
      }
      
      setResults(mockResults)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }, [filters])

  return {
    filters,
    setFilters,
    results,
    loading,
    error,
    search
  }
}

// Debounced search hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Local storage hook with TypeScript
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue
    }
    
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, storedValue])

  return [storedValue, setValue] as const
}

// Favorites management hook
export function useFavorites() {
  const [favorites, setFavorites] = useLocalStorage<string[]>('kost-favorites', [])

  const addFavorite = useCallback((propertyId: string) => {
    setFavorites(prev => [...prev.filter(id => id !== propertyId), propertyId])
  }, [setFavorites])

  const removeFavorite = useCallback((propertyId: string) => {
    setFavorites(prev => prev.filter(id => id !== propertyId))
  }, [setFavorites])

  const toggleFavorite = useCallback((propertyId: string) => {
    setFavorites(prev => 
      prev.includes(propertyId) 
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    )
  }, [setFavorites])

  const isFavorite = useCallback((propertyId: string) => {
    return favorites.includes(propertyId)
  }, [favorites])

  return {
    favorites,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorite
  }
}

// Property comparison hook
export function useComparison() {
  const [compareList, setCompareList] = useState<Property[]>([])
  const maxCompare = 3

  const addToCompare = useCallback((property: Property) => {
    setCompareList(prev => {
      if (prev.find(p => p.id === property.id)) return prev
      if (prev.length >= maxCompare) return prev
      return [...prev, property]
    })
  }, [])

  const removeFromCompare = useCallback((propertyId: string) => {
    setCompareList(prev => prev.filter(p => p.id !== propertyId))
  }, [])

  const clearComparison = useCallback(() => {
    setCompareList([])
  }, [])

  const isInComparison = useCallback((propertyId: string) => {
    return compareList.some(p => p.id === propertyId)
  }, [compareList])

  return {
    compareList,
    addToCompare,
    removeFromCompare,
    clearComparison,
    isInComparison,
    canAddMore: compareList.length < maxCompare
  }
}

// Lazy loading hook with intersection observer
export function useLazyLoading(threshold = 0.1) {
  const { ref, inView, entry } = useInView({
    threshold,
    triggerOnce: true
  })

  return { ref, inView, entry }
}

// Image loading hook
export function useImageLoad(src: string) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  useEffect(() => {
    if (!src) return

    const img = new Image()
    
    img.onload = () => {
      setLoading(false)
      setError(false)
    }
    
    img.onerror = () => {
      setLoading(false)
      setError(true)
    }
    
    img.src = src
  }, [src])

  return { loading, error }
}

// Form validation hook
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationRules: Record<keyof T, (value: any) => string | null>
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({})
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({})

  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }, [errors])

  const setTouched = useCallback((field: keyof T) => {
    setTouched(prev => ({ ...prev, [field]: true }))
  }, [])

  const validate = useCallback(() => {
    const newErrors: Partial<Record<keyof T, string>> = {}
    
    Object.keys(validationRules).forEach(field => {
      const error = validationRules[field as keyof T](values[field as keyof T])
      if (error) {
        newErrors[field as keyof T] = error
      }
    })
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [values, validationRules])

  const reset = useCallback(() => {
    setValues(initialValues)
    setErrors({})
    setTouched({})
  }, [initialValues])

  return {
    values,
    errors,
    touched,
    setValue,
    setTouched,
    validate,
    reset,
    isValid: Object.keys(errors).length === 0
  }
}

// Media query hook
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const media = window.matchMedia(query)
    setMatches(media.matches)

    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    media.addEventListener('change', listener)
    return () => media.removeEventListener('change', listener)
  }, [query])

  return matches
}

// Responsive breakpoints hook
export function useBreakpoint() {
  const isMobile = useMediaQuery('(max-width: 767px)')
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)')
  const isDesktop = useMediaQuery('(min-width: 1024px)')
  const isLarge = useMediaQuery('(min-width: 1280px)')

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLarge,
    breakpoint: isMobile ? 'mobile' : isTablet ? 'tablet' : isDesktop ? 'desktop' : 'large'
  }
}

// Scroll position hook
export function useScrollPosition() {
  const [scrollPosition, setScrollPosition] = useState(0)

  useEffect(() => {
    const updatePosition = () => {
      setScrollPosition(window.pageYOffset)
    }

    window.addEventListener('scroll', updatePosition)
    updatePosition()

    return () => window.removeEventListener('scroll', updatePosition)
  }, [])

  return scrollPosition
}

// Loading state hook
export function useLoadingState(initialState = false) {
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: initialState
  })

  const startLoading = useCallback((message?: string) => {
    setLoading({ isLoading: true, message })
  }, [])

  const stopLoading = useCallback(() => {
    setLoading({ isLoading: false })
  }, [])

  return {
    ...loading,
    startLoading,
    stopLoading
  }
}

// Error state hook
export function useErrorState() {
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: ''
  })

  const setErrorState = useCallback((message: string, code?: string) => {
    setError({ hasError: true, message, code })
  }, [])

  const clearError = useCallback(() => {
    setError({ hasError: false, message: '' })
  }, [])

  return {
    ...error,
    setError: setErrorState,
    clearError
  }
}

// Animation preferences hook
export function useReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const listener = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches)
    }

    mediaQuery.addEventListener('change', listener)
    return () => mediaQuery.removeEventListener('change', listener)
  }, [])

  return prefersReducedMotion
}
